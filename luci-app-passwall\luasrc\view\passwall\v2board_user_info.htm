<%
local api = require "luci.passwall.api"
-%>
<div id="v2board_user_info" style="margin: 10px 0;">
	<div id="user_info_content">
		<p>获取用户信息中...</p>
	</div>
</div>

<script type="text/javascript">
//<![CDATA[
function loadUserInfo() {
	XHR.get('<%=api.url("v2board_user_info")%>', null, function(x, result) {
		var userInfoContent = document.getElementById('user_info_content');
		
		if (result && result.code === 0 && result.data) {
			var userInfo = result.data;
			var html = '<table class="table" style="width: 100%;">';
			
			if (userInfo.email) {
				html += '<tr><td style="width: 30%; font-weight: bold;">邮箱:</td><td>' + userInfo.email + '</td></tr>';
			}
			
			if (userInfo.plan) {
				html += '<tr><td style="width: 30%; font-weight: bold;">套餐:</td><td>' + userInfo.plan.name + '</td></tr>';
			}
			
			if (userInfo.transfer_enable !== undefined) {
				var totalGB = Math.round(userInfo.transfer_enable / 1024 / 1024 / 1024);
				html += '<tr><td style="width: 30%; font-weight: bold;">总流量:</td><td>' + totalGB + ' GB</td></tr>';
			}
			
			if (userInfo.u !== undefined && userInfo.d !== undefined) {
				var usedGB = Math.round((userInfo.u + userInfo.d) / 1024 / 1024 / 1024);
				html += '<tr><td style="width: 30%; font-weight: bold;">已用流量:</td><td>' + usedGB + ' GB</td></tr>';
			}
			
			if (userInfo.transfer_enable !== undefined && userInfo.u !== undefined && userInfo.d !== undefined) {
				var remainingGB = Math.round((userInfo.transfer_enable - userInfo.u - userInfo.d) / 1024 / 1024 / 1024);
				html += '<tr><td style="width: 30%; font-weight: bold;">剩余流量:</td><td>' + remainingGB + ' GB</td></tr>';
			}
			
			if (userInfo.expired_at) {
				var expireDate = new Date(userInfo.expired_at * 1000);
				html += '<tr><td style="width: 30%; font-weight: bold;">到期时间:</td><td>' + expireDate.toLocaleDateString() + '</td></tr>';
			}
			
			html += '</table>';
			userInfoContent.innerHTML = html;
		} else {
			userInfoContent.innerHTML = '<p style="color: #999;">请先登录以查看用户信息</p>';
		}
	});
}

// 页面加载时获取用户信息
document.addEventListener('DOMContentLoaded', function() {
	setTimeout(loadUserInfo, 1000); // 延迟1秒加载，确保登录状态已检查
});
//]]>
</script>
