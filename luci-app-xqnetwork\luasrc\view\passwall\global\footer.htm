<%
local api = require "luci.passwall.api"
-%>
<script type="text/javascript">
	//<![CDATA[
	function go() {
		var _status = document.getElementsByClassName('_status');
		for (var i = 0; i < _status.length; i++) {
			var id = _status[i].getAttribute("socks_id");
			XHR.get('<%=api.url("socks_status")%>', {
					index: i,
					id: id
				},
				function(x, result) {
					var index = result.index;
					var div = '';
					var div1 = '<font style="font-weight:bold;" color="green">✓</font>&nbsp';
					var div2 = '<font style="font-weight:bold;" color="red">X</font>&nbsp';

					if (result.socks_status) {
						div += div1;
					} else {
						div += div2;
					}
					if (result.use_http) {
						if (result.http_status) {
							div += div1;
						} else {
							div += div2;
						}
					}
					_status[index].innerHTML = div;
				}
			);
		}

		var global_id = null;
		var global = document.getElementById("cbi-passwall-global");
		if (global) {
			var node = global.getElementsByClassName("cbi-section-node")[0];
			var node_id = node.getAttribute("id");
			global_id = node_id;
			var reg1 = new RegExp("(?<=" + node_id + "-).*?(?=(_node))")
			for (var i = 0; i < node.childNodes.length; i++) {
				if (node.childNodes[i].childNodes && node.childNodes[i].childNodes.length > 0) {
					for (var k = 0; k < node.childNodes[i].childNodes.length; k++) {
						try {
							var dom = node.childNodes[i].childNodes[k];
							if (dom.id) {
								var s = dom.id.match(reg1);
								if (s) {
									var cbi_id = global_id + "-"
									var dom_id = dom.id.split(cbi_id).join(cbi_id.split("-").join(".")).split("cbi.").join("cbid.")
									var node_select = document.getElementsByName(dom_id)[0];
									var node_select_value = node_select.value;
									if (node_select_value && node_select_value != "" && node_select_value.indexOf("_default") != 0 && node_select_value.indexOf("_direct") != 0 && node_select_value.indexOf("_blackhole") != 0) {
										if (global_id != null && node_select_value.indexOf("tcp") == 0) {
											var d = global_id + "-tcp_node";
											d = d.replace("cbi-", "cbid-").replace(new RegExp("-", 'g'), ".");
											var dom = document.getElementsByName(d)[0];
											var _node_select_value = dom.value;
											if (_node_select_value && _node_select_value != "") {
												node_select_value = _node_select_value;
											}
										}
										
										if (node_select.tagName == "INPUT") {
											node_select = document.getElementById("cbi.combobox." + dom_id);
										}

										var new_html = ""
										if (true) {
											var to_url = '<%=api.url("node_config")%>/' + node_select_value;
											if (node_select_value.indexOf("Socks_") == 0) {
												to_url = '<%=api.url("socks_config")%>/' + node_select_value.substring("Socks_".length);
											}
											var new_a = document.createElement("a");
											new_a.innerHTML = "<%:Edit%>";
											new_a.href = "#";
											new_a.setAttribute("onclick", "location.href='" + to_url + "'");
											new_html = new_a.outerHTML;
										}

										if (s[0] == "tcp" || s[0] == "udp") {
											var log_a = document.createElement("a");
											log_a.innerHTML = "<%:Log%>";
											log_a.href = "#";
											log_a.setAttribute("onclick", "window.open('" + '<%=api.url("get_redir_log")%>' + "?name=default&proto=" + s[0] + "', '_blank')");
											new_html += "&nbsp&nbsp" + log_a.outerHTML;
										}

										node_select.insertAdjacentHTML("afterend", "&nbsp&nbsp" + new_html);
									}
								}
							}
						} catch(err) {
						}
					}
				}
			}
		}

		var socks = document.getElementById("cbi-passwall-socks");
		if (socks) {
			var socks_enabled_dom = document.getElementById(global_id + "-socks_enabled");
			socks_enabled_dom.parentNode.removeChild(socks_enabled_dom);
			var descr = socks.getElementsByClassName("cbi-section-descr")[0];
			descr.outerHTML = socks_enabled_dom.outerHTML;
			rows = socks.getElementsByClassName("cbi-section-table-row");
			for (var i = 0; i < rows.length; i++) {
				try {
					var row = rows[i];
					var id = row.id;
					if (!id) continue;
					var dom_id = id + "-node";
					var node = document.getElementById(dom_id);
					var dom_id = dom_id.replace("cbi-", "cbid-").replace(new RegExp("-", 'g'), ".");
					var node_select = document.getElementsByName(dom_id)[0];
					var node_select_value = node_select.value;
					if (node_select_value && node_select_value != "") {
						var v = document.getElementById(dom_id + "-" + node_select_value);
						if (v) {
							node_select.title = v.text;
						} else {
							node_select.title = node_select.options[node_select.options.selectedIndex].text;
						}

						var new_html = ""

						var new_a = document.createElement("a");
						new_a.innerHTML = "<%:Edit%>";
						new_a.href = "#";
						new_a.setAttribute("onclick","location.href='" + '<%=api.url("node_config")%>' + "/" + node_select_value + "'");
						new_html = new_a.outerHTML;
						
						var log_a = document.createElement("a");
						log_a.innerHTML = "<%:Log%>";
						log_a.href = "#";
						log_a.setAttribute("onclick", "window.open('" + '<%=api.url("get_socks_log")%>' + "?name=" + id.replace("cbi-passwall-", "") + "', '_blank')");
						new_html += "&nbsp" + log_a.outerHTML;

						node_select.insertAdjacentHTML("afterend", "&nbsp&nbsp" + new_html);
					}
				} catch(err) {
				}
			}
		}
	}
	setTimeout("go()", 1000);

	document.addEventListener("DOMContentLoaded", function () {
		setTimeout(function () {
			var selects = document.querySelectorAll("select[id*='dns_shunt']");
			selects.forEach(function (select, index) {
				if (select.value === "chinadns-ng") {
					addLogLink(select);
				}
				select.addEventListener("change", function () {
					var existingLogLink = select.parentElement.querySelector("a.log-link");
					if (existingLogLink) {
						existingLogLink.remove();
					}
					if (select.value === "chinadns-ng") {
						addLogLink(select);
					}
				});
			});
			function addLogLink(select) {
				var logLink = document.createElement("a");
				logLink.innerHTML = "<%:Log%>";
				logLink.href = "#";
				logLink.className = "log-link";
				logLink.style.marginLeft = "10px";
				logLink.setAttribute("onclick", "window.open('" + '<%=api.url("get_chinadns_log")%>' + "?flag=default', '_blank')");
				select.insertAdjacentElement("afterend", logLink);
			}
		}, 1000);
	});
	//]]>
</script>
