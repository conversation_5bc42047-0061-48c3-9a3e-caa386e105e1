﻿# XQNetwork 椤圭洰鏀归€犳€荤粨

## 椤圭洰姒傝堪

鎴愬姛灏?`luci-app-xqnetwork` 鏀归€犱负 `luci-app-xqnetwork`锛屼笓闂ㄧ敤浜庡鎺ラ潰鏉?API锛屽疄鐜颁簡鐢ㄦ埛瑕佹眰鐨勬墍鏈夊姛鑳姐€?
## 鏈€鏂版洿鏂帮紙鎸夌敤鎴疯姹傦級

### 鉁?API 鍚庣瀵规帴
- 浣跨敤姝ｇ‘鐨勭櫥褰?API锛歚/api/v1/passport/auth/login`
- 浣跨敤姝ｇ‘鐨勮闃?API锛歚/api/v1/user/getSubscribe`
- 娣诲姞鑷畾涔夎姹傚ご锛歚User-Agent: xqnetworkxqnetwork`
- 浣跨敤 `Authorization` 澶翠紶閫?Token

### 鉁?澶?API 鍦板潃鏀寔
- 閫氳繃 OSS 閰嶇疆鏂囦欢鍔ㄦ€佽幏鍙?API 鍦板潃鍒楄〃
- 鏀寔澶氫釜 API 鍦板潃鑷姩閲嶈瘯鏈哄埗
- 绗竴涓湴鍧€瓒呮椂鑷姩灏濊瘯涓嬩竴涓?
### 鉁?鍏ㄥ眬鍚嶇О淇敼
- 灏嗘墍鏈?`xqnetwork` 寮曠敤鏀逛负 `xqnetwork`
- 灏嗘墍鏈?`v2board` 寮曠敤鏀逛负 `xqnetwork`
- 鏇存柊鎵€鏈夋彁绀哄拰鍛藉悕涓?XQNetwork

### 鉁?閰嶇疆鏂囦欢绠＄悊
- 绔欑偣淇℃伅閫氳繃閰嶇疆鏂囦欢鑾峰彇锛岀敤鎴锋棤闇€濉啓
- OSS 閰嶇疆鏂囦欢鏀寔 base64 缂栫爜
- 鍔ㄦ€侀厤缃枃浠跺湴鍧€绠＄悊

## 瀹屾垚鐨勫姛鑳?
### 鉁?1. V2Board 鐧诲綍闆嗘垚
- **鏂板妯″潡**: `luci-app-xqnetwork/luasrc/xqnetwork/v2board_api.lua`
- **鍔熻兘瀹炵幇**:
  - 閭瀵嗙爜鐧诲綍 V2Board 闈㈡澘
  - 鑷姩绠＄悊 Token 鍜岃繃鏈熸椂闂?  - 鑾峰彇鐢ㄦ埛淇℃伅锛堝椁愩€佹祦閲忋€佸埌鏈熸椂闂达級
  - 鑾峰彇璁㈤槄閾炬帴骞惰嚜鍔ㄥ悓姝ヨ妭鐐?
### 鉁?2. 璁㈤槄鎷夊彇鍔熻兘
- **淇敼鏂囦欢**: `luci-app-xqnetwork/root/usr/share/xqnetwork/subscribe.lua`
- **鍔熻兘瀹炵幇**:
  - 鑷姩浠?V2Board 鑾峰彇璁㈤槄閾炬帴
  - 瑙ｆ瀽璁㈤槄鍐呭锛堟敮鎸?base64 缂栫爜锛?  - 鑷姩鍒涘缓涓存椂璁㈤槄閰嶇疆
  - 涓庣幇鏈夎闃呯郴缁熸棤缂濋泦鎴?
### 鉁?3. 鑺傜偣淇℃伅闅愯棌鏈哄埗
- **鏍稿績鍔熻兘**:
  - **IP 鍦板潃闅愯棌**: `*************` 鈫?`192.***.***.***`
  - **IPv6 鍦板潃闅愯棌**: `2001:db8::1` 鈫?`2001:***::***`
  - **鍩熷悕闅愯棌**: `node.example.com` 鈫?`***.com`
- **瀹炵幇浣嶇疆**:
  - 鑺傜偣鍒楄〃鏄剧ず: `luci-app-xqnetwork/luasrc/model/cbi/xqnetwork/client/node_list.lua`
  - 璁㈤槄澶勭悊: `luci-app-xqnetwork/root/usr/share/xqnetwork/subscribe.lua`
  - API 妯″潡: `luci-app-xqnetwork/luasrc/xqnetwork/v2board_api.lua`

### 鉁?4. 鏃ュ織杩囨护绯荤粺
- **鏂板妯″潡**: `luci-app-xqnetwork/root/usr/share/xqnetwork/log_filter.lua`
- **鍔熻兘瀹炵幇**:
  - 瀹炴椂杩囨护鏃ュ織涓殑鏁忔劅淇℃伅
  - 鏀寔澶氱鏃ュ織鏂囦欢绫诲瀷
  - 姣?30 绉掕嚜鍔ㄦ墽琛岃繃婊?  - 涓庝富鏈嶅姟闆嗘垚鍚姩/鍋滄

### 鉁?5. 鐢ㄦ埛鐣岄潰鏀归€?- **鏂板椤甸潰**: V2Board 鐧诲綍椤甸潰
  - `luci-app-xqnetwork/luasrc/model/cbi/xqnetwork/client/v2board_login.lua`
  - `luci-app-xqnetwork/luasrc/view/xqnetwork/v2board_*.htm`
- **鐣岄潰浼樺寲**:
  - 灏?V2Board 鐧诲綍璁句负棣栭〉
  - 闅愯棌涓嶅繀瑕佺殑鍔熻兘妯″潡
  - 淇濈暀鏍稿績浠ｇ悊鍔熻兘
  - 鏇存敼搴旂敤鍚嶇О涓?"XQNetwork"

### 鉁?6. 椤圭洰閲嶅懡鍚嶅拰鎵撳寘
- **椤圭洰閲嶅懡鍚?*: `luci-app-xqnetwork` 鈫?`luci-app-xqnetwork`
- **閰嶇疆鏇存柊**: 
  - Makefile 涓殑鍖呭悕鍜屾爣棰?  - 鎺у埗鍣ㄤ腑鐨?ACL 渚濊禆
  - 榛樿閰嶇疆鏂囦欢娣诲姞 v2board 閰嶇疆娈?- **鏂囨。瀹屽杽**: README.md銆乁SAGE.md銆佸畨瑁?鍗歌浇鑴氭湰

## 鎶€鏈疄鐜扮粏鑺?
### V2Board API 闆嗘垚
```lua
-- 鐧诲綍娴佺▼
1. 鐢ㄦ埛杈撳叆闈㈡澘鍦板潃銆侀偖绠便€佸瘑鐮?2. 璋冪敤 V2Board 鐧诲綍 API
3. 鑾峰彇骞朵繚瀛?Token
4. 璁剧疆杩囨湡鏃堕棿锛?4灏忔椂锛?
-- 璁㈤槄鍚屾
1. 浣跨敤 Token 鑾峰彇璁㈤槄閾炬帴
2. 涓嬭浇璁㈤槄鍐呭
3. 瑙ｆ瀽鑺傜偣淇℃伅
4. 搴旂敤闅愯棌瑙勫垯
5. 娣诲姞鍒拌妭鐐瑰垪琛?```

### 闅愮淇濇姢鏈哄埗
```lua
-- 鑺傜偣淇℃伅闅愯棌
function hide_node_info(node_data)
    -- IP 鍦板潃澶勭悊
    if datatypes.ipaddr(address) then
        parts = split(address, ".")
        return parts[1] .. ".***.***.***"
    end
    -- 鍩熷悕澶勭悊
    domain_parts = split(address, ".")
    return "***." .. domain_parts[#domain_parts]
end

-- 鏃ュ織杩囨护
function filter_log_content(content)
    -- 鏇挎崲鎵€鏈夋晱鎰熶俊鎭?    for _, info in ipairs(sensitive_info) do
        content = content:gsub(info, hidden_version)
    end
    return content
end
```

### 鐣岄潰闆嗘垚
```javascript
// AJAX 璋冪敤绀轰緥
XHR.post('/admin/services/xqnetwork/v2board_login', {
    base_url: baseUrl,
    email: email,
    password: password
}, function(x, result) {
    // 澶勭悊鐧诲綍缁撴灉
});
```

## 鏂囦欢缁撴瀯

```
luci-app-xqnetwork/
鈹溾攢鈹€ Makefile                           # 鍖呴厤缃枃浠?鈹溾攢鈹€ luasrc/
鈹?  鈹溾攢鈹€ controller/
鈹?  鈹?  鈹斺攢鈹€ xqnetwork.lua              # 鎺у埗鍣紙宸蹭慨鏀癸級
鈹?  鈹溾攢鈹€ model/cbi/xqnetwork/client/
鈹?  鈹?  鈹溾攢鈹€ v2board_login.lua         # V2Board 鐧诲綍椤甸潰锛堟柊澧烇級
鈹?  鈹?  鈹斺攢鈹€ node_list.lua             # 鑺傜偣鍒楄〃锛堝凡淇敼锛?鈹?  鈹溾攢鈹€ xqnetwork/
鈹?  鈹?  鈹溾攢鈹€ v2board_api.lua           # V2Board API 妯″潡锛堟柊澧烇級
鈹?  鈹?  鈹斺攢鈹€ api.lua                   # 鍘熸湁 API 妯″潡
鈹?  鈹斺攢鈹€ view/xqnetwork/
鈹?      鈹溾攢鈹€ v2board_status.htm        # 鐧诲綍鐘舵€佹ā鏉匡紙鏂板锛?鈹?      鈹溾攢鈹€ v2board_user_info.htm     # 鐢ㄦ埛淇℃伅妯℃澘锛堟柊澧烇級
鈹?      鈹斺攢鈹€ v2board_actions.htm       # 鎿嶄綔鎸夐挳妯℃澘锛堟柊澧烇級
鈹斺攢鈹€ root/
    鈹斺攢鈹€ usr/share/xqnetwork/
        鈹溾攢鈹€ log_filter.lua            # 鏃ュ織杩囨护鑴氭湰锛堟柊澧烇級
        鈹溾攢鈹€ subscribe.lua             # 璁㈤槄鑴氭湰锛堝凡淇敼锛?        鈹溾攢鈹€ app.sh                    # 涓诲簲鐢ㄨ剼鏈紙宸蹭慨鏀癸級
        鈹斺攢鈹€ 0_default_config          # 榛樿閰嶇疆锛堝凡淇敼锛?```

## 瀹夎鍜屼娇鐢?
### 瀹夎鏂规硶
1. 浣跨敤鎻愪緵鐨?`install.sh` 鑴氭湰鑷姩瀹夎
2. 鎴栨墜鍔ㄥ鍒舵枃浠跺埌瀵瑰簲鐩綍

### 浣跨敤娴佺▼
1. 璁块棶璺敱鍣ㄧ鐞嗙晫闈?2. 杩涘叆 "XQNetwork" 鈫?"V2Board Login"
3. 濉啓 V2Board 闈㈡澘淇℃伅骞剁櫥褰?4. 鐐瑰嚮"鍚屾鑺傜偣"鑾峰彇鑺傜偣鍒楄〃
5. 鍦?Node List"涓煡鐪嬪拰浣跨敤鑺傜偣

## 瀹夊叏鐗规€?
### 闅愮淇濇姢
- 鉁?鑺傜偣 IP 鍦板潃鑷姩闅愯棌
- 鉁?鑺傜偣鍩熷悕淇℃伅鑷姩闅愯棌
- 鉁?鏃ュ織鏂囦欢瀹炴椂杩囨护
- 鉁?浠呭 V2Board 鑺傜偣鐢熸晥

### 鏁版嵁瀹夊叏
- 鉁?Token 鑷姩绠＄悊鍜岃繃鏈?- 鉁?瀵嗙爜涓嶆槑鏂囧瓨鍌ㄥ湪鏃ュ織涓?- 鉁?鏁忔劅淇℃伅涓嶅湪鐣岄潰鏄剧ず

## 鍏煎鎬?
### 淇濇寔鍏煎
- 鉁?鍘熸湁 xqnetwork 鍔熻兘瀹屽叏淇濈暀
- 鉁?鐜版湁閰嶇疆鏂囦欢鍚戜笅鍏煎
- 鉁?鏀寔鎵€鏈夊師鏈変唬鐞嗗崗璁?
### 鏂板鍔熻兘
- 鉁?V2Board 涓撶敤闆嗘垚
- 鉁?澧炲己鐨勯殣绉佷繚鎶?- 鉁?绠€鍖栫殑鐢ㄦ埛鐣岄潰

## 娴嬭瘯鍜岄獙璇?
### 鍔熻兘娴嬭瘯
- 鉁?V2Board 鐧诲綍鍔熻兘
- 鉁?璁㈤槄鍚屾鍔熻兘
- 鉁?鑺傜偣淇℃伅闅愯棌
- 鉁?鏃ュ織杩囨护鍔熻兘
- 鉁?鐣岄潰鏄剧ず姝ｅ父

### 浠ｇ爜璐ㄩ噺
- 鉁?Lua 璇硶妫€鏌ラ€氳繃
- 鉁?鏂囦欢缁撴瀯瀹屾暣
- 鉁?閰嶇疆鏂囦欢姝ｇ‘
- 鉁?妯℃澘鏂囦欢瀹屾暣

## 椤圭洰浜や粯

### 浜や粯鍐呭
1. **瀹屾暣鐨勫簲鐢ㄥ寘**: `luci-app-xqnetwork/`
2. **瀹夎鑴氭湰**: `install.sh`
3. **鍗歌浇鑴氭湰**: `uninstall.sh`
4. **娴嬭瘯鑴氭湰**: `test.sh`
5. **浣跨敤鏂囨。**: `USAGE.md`
6. **椤圭洰璇存槑**: `README.md`

### 鍚庣画缁存姢
- 寤鸿瀹氭湡鏇存柊浠ヨ幏寰楁渶鏂板姛鑳?- 鍏虫敞 V2Board API 鍙樺寲
- 鏍规嵁鐢ㄦ埛鍙嶉浼樺寲鍔熻兘

## 鎬荤粨

鎴愬姛瀹屾垚浜嗙敤鎴疯姹傜殑鎵€鏈夊姛鑳斤細
1. 鉁?瀵规帴 V2Board 鐧诲綍 API
2. 鉁?鑷姩鎷夊彇璁㈤槄骞舵樉绀鸿妭鐐?3. 鉁?闅愯棌鑺傜偣鏁忔劅淇℃伅锛圛P銆佸煙鍚嶇瓑锛?4. 鉁?杩囨护鏃ュ織涓殑鏁忔劅淇℃伅
5. 鉁?绠€鍖栫晫闈笓娉ㄦ牳蹇冨姛鑳?
椤圭洰宸插噯澶囧氨缁紝鍙互鐩存帴閮ㄧ讲浣跨敤銆?
