
config global
	option enabled '0'
	option socks_enabled '0'
	option tcp_node_socks_port '1070'
	option filter_proxy_ipv6 '1'
	option dns_shunt 'chinadns-ng'
	option dns_mode 'tcp'
	option remote_dns '*******'
	list smartdns_remote_dns 'https://*******/dns-query'
	option use_default_dns 'direct'
	option chinadns_ng_default_tag 'none'
	option dns_redirect '1'
	option use_direct_list '1'
	option use_proxy_list '1'
	option use_block_list '1'
	option use_gfw_list '1'
	option chn_list 'direct'
	option tcp_proxy_mode 'proxy'
	option udp_proxy_mode 'proxy'
	option localhost_proxy '1'
	option client_proxy '1'
	option acl_enable '0'
	option log_tcp '0'
	option log_udp '0'
	option loglevel 'error'
	option trojan_loglevel '4'
	option log_chinadns_ng '0'

config global_haproxy
	option balancing_enable '0'

config global_delay
	option start_daemon '1'
	option start_delay '60'

config global_forwarding
	option tcp_no_redir_ports 'disable'
	option udp_no_redir_ports 'disable'
	option tcp_proxy_drop_ports 'disable'
	option udp_proxy_drop_ports '443'
	option tcp_redir_ports '22,25,53,143,465,587,853,993,995,80,443'
	option udp_redir_ports '1:65535'
	option accept_icmp '0'
	option use_nft '0'
	option tcp_proxy_way 'redirect'
	option ipv6_tproxy '0'

config global_xray
	option sniffing_override_dest '0'

config global_singbox
	option sniff_override_destination '0'

config global_other
	option auto_detection_time 'tcping'
	option show_node_info '0'

config global_rules
	option auto_update '0'
	option chnlist_update '1'
	option chnroute_update '1'
	option chnroute6_update '1'
	option gfwlist_update '1'
	option geosite_update '0'
	option geoip_update '0'
	list gfwlist_url 'https://fastly.jsdelivr.net/gh/Loyalsoldier/v2ray-rules-dat@release/gfw.txt'
	list chnroute_url 'https://ispip.clang.cn/all_cn.txt'
	list chnroute_url 'https://fastly.jsdelivr.net/gh/gaoyifan/china-operator-ip@ip-lists/china.txt'
	list chnroute6_url 'https://ispip.clang.cn/all_cn_ipv6.txt'
	list chnroute6_url 'https://fastly.jsdelivr.net/gh/gaoyifan/china-operator-ip@ip-lists/china6.txt'
	list chnlist_url 'https://fastly.jsdelivr.net/gh/felixonmars/dnsmasq-china-list/accelerated-domains.china.conf'
	list chnlist_url 'https://fastly.jsdelivr.net/gh/felixonmars/dnsmasq-china-list/apple.china.conf'
	option v2ray_location_asset '/usr/share/v2ray/'
	option geoip_url 'https://github.com/Loyalsoldier/v2ray-rules-dat/releases/latest/download/geoip.dat'
	option geosite_url 'https://github.com/Loyalsoldier/v2ray-rules-dat/releases/latest/download/geosite.dat'

config global_app
	option sing_box_file '/usr/bin/sing-box'
	option xray_file '/usr/bin/xray'
	option hysteria_file '/usr/bin/hysteria'

config global_subscribe
	option filter_keyword_mode '1'
	list filter_discard_list '距离下次重置剩余'
	list filter_discard_list '套餐到期'
	list filter_discard_list '过期时间'
	list filter_discard_list '剩余流量'
	list filter_discard_list 'QQ群'
	list filter_discard_list '官网'

config nodes 'myshunt'
	option remarks '分流总节点'
	option type 'Xray'
	option protocol '_shunt'
	option DirectGame '_direct'
	option ProxyGame '_default'
	option AIGC '_default'
	option Streaming '_default'
	option Proxy '_default'
	option Direct '_direct'
	option default_node '_direct'
	option domainStrategy 'IPOnDemand'

config shunt_rules 'DirectGame'
	option remarks 'DirectGame'
	option domain_list '# steam直连域名获取国内CDN走国内线路下载
cm.steampowered.com
steamserver.net

# steam国内CDN华为云
steampipe.steamcontent.tnkjmec.com
# steam国内CDN白山云
st.dl.eccdnx.com
st.dl.bscstorage.net
st.dl.pinyuncloud.com
# steam国内CDN新流云(原金山云)(支持ipv6)
dl.steam.clngaa.com
# steam国内CDN网宿
cdn.mileweb.cs.steampowered.com.8686c.com
cdn-ws.content.steamchina.com
# steam国内CDN腾讯云 (蒸汽中国独占)
cdn-qc.content.steamchina.com
# steam国内CDN阿里云(支持ipv6)
cdn-ali.content.steamchina.com
xz.pphimalayanrt.com
lv.queniujq.cn
alibaba.cdn.steampipe.steamcontent.com

# 国内游戏geosite域名
geosite:category-games@cn'
	option ip_list '# steam直连IP
************/24
************/23
***********/24
************/24
************/24
************/24
*************/22
*************/24
*************/23
*************/24
*************/22
*************/23
*************/23
*************/24
*************/21
*************/21
************/23
************/22
***********/22
***********/24
************/22
************/22
*************/24'

config shunt_rules 'ProxyGame'
	option remarks 'ProxyGame'
	option domain_list '# steam 商店/客服/聊天/网页布局/API/二维码/Google云同步 代理URL
steamcommunity.com
www.steamcommunity.com
store.steampowered.com
checkout.steampowered.com
api.steampowered.com
help.steampowered.com
login.steampowered.com
store.akamai.steamstatic.com
steambroadcast.akamaized.net
steamvideo-a.akamaihd.net
steamusercontent-a.akamaihd.net
steamstore-a.akamaihd.net
steamcommunity-a.akamaihd.net
steamcdn-a.akamaihd.net
steamuserimages-a.akamaihd.net
community.akamai.steamstatic.com
avatars.akamai.steamstatic.com
community.steamstatic.com
cdn.akamai.steamstatic.com
avatars.steamstatic.com
shared.akamai.steamstatic.com
clan.akamai.steamstatic.com
cdn.cloudflare.steamstatic.com
community.cloudflare.steamstatic.com
store.cloudflare.steamstatic.com
avatars.cloudflare.steamstatic.com
clan.cloudflare.steamstatic.com
shared.cloudflare.steamstatic.com
steam-chat.com
steamcloud-ugc.storage.googleapis.com
steamcloud-eu-ams.storage.googleapis.com
steamcloud-eu-fra.storage.googleapis.com
steamcloud-finland.storage.googleapis.com
steamcloud-saopaulo.storage.googleapis.com
steamcloud-singapore.storage.googleapis.com
steamcloud-sydney.storage.googleapis.com
steamcloud-taiwan.storage.googleapis.com
steamcloud-eu.storage.googleapis.com

geosite:category-games'

config shunt_rules 'AIGC'
	option remarks 'AIGC'
	option domain_list 'geosite:category-ai-!cn
geosite:apple-intelligence'

config shunt_rules 'Streaming'
	option remarks 'Streaming'
	option domain_list 'geosite:netflix
geosite:disney'

config shunt_rules 'Proxy'
	option remarks 'Proxy'
	option domain_list 'geosite:geolocation-!cn'
	option ip_list '*************/20
**********/22
***********/24
*************/24
***********/24
*******
*******
**************
**************
*******
*******
*******
*******
149.112.************:67c:4e8::/48
2001:b28:f23c::/48
2001:b28:f23d::/48
2001:b28:f23f::/48
2001:b28:f242::/48
2001:4860:4860::8888
2001:4860:4860::8844
2606:4700:4700::1111
2606:4700:4700::1001'

config shunt_rules 'Direct'
	option remarks 'Direct'
	option domain_list 'geosite:cn'
	option ip_list '*********/32
*********/32
************/32
************/32
***************/32
***************/32
**********/32
************/32
geoip:cn
geoip:private'
