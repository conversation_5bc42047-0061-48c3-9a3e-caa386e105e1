# XQNetwork 配置示例

## OSS 配置文件

### 1. 本地配置文件
位置：`/usr/share/xqnetwork/config.json`

```json
{
  "oss_config_url": "https://xxx.obs.cn-south-4.myhuaweicloud.com/host.json",
  "app_name": "XQNetwork",
  "version": "1.0.0",
  "description": "XQNetwork API Configuration"
}
```

### 2. OSS 远程配置文件
文件名：`host.json`（或任意文件名）
内容：base64 编码的 JSON

#### 原始 JSON 内容：
```json
{
  "url": [
    "https://api1.example.com", 
    "https://api2.example.com", 
    "https://api3.example.com" 
  ]
}
```

#### Base64 编码后：
```
ewogICJ1cmwiOiBbCiAgICAiaHR0cHM6Ly9hcGkxLmV4YW1wbGUuY29tIiwgCiAgICAiaHR0cHM6Ly9hcGkyLmV4YW1wbGUuY29tIiwgCiAgICAiaHR0cHM6Ly9hcGkzLmV4YW1wbGUuY29tIiAKICBdCn0=
```

## API 接口说明

### 1. 登录接口
- **URL**: `/api/v1/passport/auth/login`
- **方法**: POST
- **请求头**: 
  - `Content-Type: application/json`
  - `User-Agent: xqnetworkpasswall`
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- **响应**:
```json
{
  "data": "auth_token_here"
}
```

### 2. 用户信息接口
- **URL**: `/api/v1/user/info`
- **方法**: GET
- **请求头**: 
  - `Authorization: auth_token_here`
  - `User-Agent: xqnetworkpasswall`
- **响应**:
```json
{
  "data": {
    "email": "<EMAIL>",
    "plan": {
      "name": "Premium Plan"
    },
    "transfer_enable": 107374182400,
    "u": 1073741824,
    "d": 2147483648,
    "expired_at": 1735689600
  }
}
```

### 3. 订阅接口
- **URL**: `/api/v1/user/getSubscribe`
- **方法**: GET
- **请求头**: 
  - `Authorization: auth_token_here`
  - `User-Agent: xqnetworkpasswall`
- **响应**:
```json
{
  "data": {
    "subscribe_url": "https://api.example.com/subscribe/token_here"
  }
}
```

## 重试机制

### API 地址重试逻辑
1. 从 OSS 获取 API 地址列表
2. 按顺序尝试每个 API 地址
3. 每个地址超时时间：10 秒
4. 重试次数：1 次
5. 如果所有地址都失败，返回错误

### 示例重试流程
```
1. 尝试 https://api1.example.com/api/v1/passport/auth/login
   - 超时或失败
2. 尝试 https://api2.example.com/api/v1/passport/auth/login
   - 超时或失败
3. 尝试 https://api3.example.com/api/v1/passport/auth/login
   - 成功，返回结果
```

## 节点隐藏规则

### IP 地址隐藏
- IPv4: `*************` → `192.***.***.***`
- IPv6: `2001:db8::1` → `2001:***::***`

### 域名隐藏
- `node.example.com` → `***.com`
- `api.test.org` → `***.org`

### 隐藏范围
- 节点列表显示
- 日志文件内容
- 配置文件导出
- 状态页面显示

## 配置文件位置

### 主配置
- `/etc/config/xqnetwork` - 主配置文件
- `/usr/share/xqnetwork/config.json` - 应用配置
- `/usr/share/xqnetwork/0_default_config` - 默认配置

### 日志文件
- `/tmp/log/xqnetwork.log` - 主日志
- `/tmp/log/xqnetwork_server.log` - 服务端日志
- `/tmp/etc/xqnetwork/` - 运行时文件

### 规则文件
- `/usr/share/xqnetwork/rules/` - 分流规则目录

## 安全注意事项

1. **Token 管理**
   - Token 自动过期（24小时）
   - 本地加密存储
   - 定期刷新机制

2. **敏感信息保护**
   - 节点地址自动隐藏
   - 日志实时过滤
   - 配置文件权限控制

3. **网络安全**
   - HTTPS 强制加密
   - 请求头标识
   - 超时控制

## 故障排除

### 常见问题
1. **登录失败**
   - 检查邮箱密码
   - 确认 API 地址可访问
   - 查看日志错误信息

2. **节点同步失败**
   - 确认登录状态
   - 检查订阅接口响应
   - 验证网络连接

3. **配置文件错误**
   - 检查 JSON 格式
   - 验证 base64 编码
   - 确认文件权限

### 调试命令
```bash
# 查看日志
tail -f /tmp/log/xqnetwork.log

# 测试 API 连接
curl -H "User-Agent: xqnetworkpasswall" https://api.example.com/api/v1/user/info

# 检查配置
cat /etc/config/xqnetwork
```
