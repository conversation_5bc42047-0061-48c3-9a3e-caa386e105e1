﻿# XQNetwork 閰嶇疆绀轰緥

## OSS 閰嶇疆鏂囦欢

### 1. 鏈湴閰嶇疆鏂囦欢
浣嶇疆锛歚/usr/share/xqnetwork/config.json`

```json
{
  "oss_config_url": "https://xxx.obs.cn-south-4.myhuaweicloud.com/host.json",
  "app_name": "XQNetwork",
  "version": "1.0.0",
  "description": "XQNetwork API Configuration"
}
```

### 2. OSS 杩滅▼閰嶇疆鏂囦欢
鏂囦欢鍚嶏細`host.json`锛堟垨浠绘剰鏂囦欢鍚嶏級
鍐呭锛歜ase64 缂栫爜鐨?JSON

#### 鍘熷 JSON 鍐呭锛?```json
{
  "url": [
    "https://api1.example.com", 
    "https://api2.example.com", 
    "https://api3.example.com" 
  ]
}
```

#### Base64 缂栫爜鍚庯細
```
ewogICJ1cmwiOiBbCiAgICAiaHR0cHM6Ly9hcGkxLmV4YW1wbGUuY29tIiwgCiAgICAiaHR0cHM6Ly9hcGkyLmV4YW1wbGUuY29tIiwgCiAgICAiaHR0cHM6Ly9hcGkzLmV4YW1wbGUuY29tIiAKICBdCn0=
```

## API 鎺ュ彛璇存槑

### 1. 鐧诲綍鎺ュ彛
- **URL**: `/api/v1/passport/auth/login`
- **鏂规硶**: POST
- **璇锋眰澶?*: 
  - `Content-Type: application/json`
  - `User-Agent: xqnetworkxqnetwork`
- **璇锋眰浣?*:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- **鍝嶅簲**:
```json
{
  "data": "auth_token_here"
}
```

### 2. 鐢ㄦ埛淇℃伅鎺ュ彛
- **URL**: `/api/v1/user/info`
- **鏂规硶**: GET
- **璇锋眰澶?*: 
  - `Authorization: auth_token_here`
  - `User-Agent: xqnetworkxqnetwork`
- **鍝嶅簲**:
```json
{
  "data": {
    "email": "<EMAIL>",
    "plan": {
      "name": "Premium Plan"
    },
    "transfer_enable": 107374182400,
    "u": 1073741824,
    "d": 2147483648,
    "expired_at": 1735689600
  }
}
```

### 3. 璁㈤槄鎺ュ彛
- **URL**: `/api/v1/user/getSubscribe`
- **鏂规硶**: GET
- **璇锋眰澶?*: 
  - `Authorization: auth_token_here`
  - `User-Agent: xqnetworkxqnetwork`
- **鍝嶅簲**:
```json
{
  "data": {
    "subscribe_url": "https://api.example.com/subscribe/token_here"
  }
}
```

## 閲嶈瘯鏈哄埗

### API 鍦板潃閲嶈瘯閫昏緫
1. 浠?OSS 鑾峰彇 API 鍦板潃鍒楄〃
2. 鎸夐『搴忓皾璇曟瘡涓?API 鍦板潃
3. 姣忎釜鍦板潃瓒呮椂鏃堕棿锛?0 绉?4. 閲嶈瘯娆℃暟锛? 娆?5. 濡傛灉鎵€鏈夊湴鍧€閮藉け璐ワ紝杩斿洖閿欒

### 绀轰緥閲嶈瘯娴佺▼
```
1. 灏濊瘯 https://api1.example.com/api/v1/passport/auth/login
   - 瓒呮椂鎴栧け璐?2. 灏濊瘯 https://api2.example.com/api/v1/passport/auth/login
   - 瓒呮椂鎴栧け璐?3. 灏濊瘯 https://api3.example.com/api/v1/passport/auth/login
   - 鎴愬姛锛岃繑鍥炵粨鏋?```

## 鑺傜偣闅愯棌瑙勫垯

### IP 鍦板潃闅愯棌
- IPv4: `*************` 鈫?`192.***.***.***`
- IPv6: `2001:db8::1` 鈫?`2001:***::***`

### 鍩熷悕闅愯棌
- `node.example.com` 鈫?`***.com`
- `api.test.org` 鈫?`***.org`

### 闅愯棌鑼冨洿
- 鑺傜偣鍒楄〃鏄剧ず
- 鏃ュ織鏂囦欢鍐呭
- 閰嶇疆鏂囦欢瀵煎嚭
- 鐘舵€侀〉闈㈡樉绀?
## 閰嶇疆鏂囦欢浣嶇疆

### 涓婚厤缃?- `/etc/config/xqnetwork` - 涓婚厤缃枃浠?- `/usr/share/xqnetwork/config.json` - 搴旂敤閰嶇疆
- `/usr/share/xqnetwork/0_default_config` - 榛樿閰嶇疆

### 鏃ュ織鏂囦欢
- `/tmp/log/xqnetwork.log` - 涓绘棩蹇?- `/tmp/log/xqnetwork_server.log` - 鏈嶅姟绔棩蹇?- `/tmp/etc/xqnetwork/` - 杩愯鏃舵枃浠?
### 瑙勫垯鏂囦欢
- `/usr/share/xqnetwork/rules/` - 鍒嗘祦瑙勫垯鐩綍

## 瀹夊叏娉ㄦ剰浜嬮」

1. **Token 绠＄悊**
   - Token 鑷姩杩囨湡锛?4灏忔椂锛?   - 鏈湴鍔犲瘑瀛樺偍
   - 瀹氭湡鍒锋柊鏈哄埗

2. **鏁忔劅淇℃伅淇濇姢**
   - 鑺傜偣鍦板潃鑷姩闅愯棌
   - 鏃ュ織瀹炴椂杩囨护
   - 閰嶇疆鏂囦欢鏉冮檺鎺у埗

3. **缃戠粶瀹夊叏**
   - HTTPS 寮哄埗鍔犲瘑
   - 璇锋眰澶存爣璇?   - 瓒呮椂鎺у埗

## 鏁呴殰鎺掗櫎

### 甯歌闂
1. **鐧诲綍澶辫触**
   - 妫€鏌ラ偖绠卞瘑鐮?   - 纭 API 鍦板潃鍙闂?   - 鏌ョ湅鏃ュ織閿欒淇℃伅

2. **鑺傜偣鍚屾澶辫触**
   - 纭鐧诲綍鐘舵€?   - 妫€鏌ヨ闃呮帴鍙ｅ搷搴?   - 楠岃瘉缃戠粶杩炴帴

3. **閰嶇疆鏂囦欢閿欒**
   - 妫€鏌?JSON 鏍煎紡
   - 楠岃瘉 base64 缂栫爜
   - 纭鏂囦欢鏉冮檺

### 璋冭瘯鍛戒护
```bash
# 鏌ョ湅鏃ュ織
tail -f /tmp/log/xqnetwork.log

# 娴嬭瘯 API 杩炴帴
curl -H "User-Agent: xqnetworkxqnetwork" https://api.example.com/api/v1/user/info

# 妫€鏌ラ厤缃?cat /etc/config/xqnetwork
```

