<%
local api = require "luci.passwall.api"
local translate = luci.i18n.translate
local total_lines_text = translate("Total Lines")
-%>
<script type="text/javascript">
	//<![CDATA[
	function read_gfw() {
		fetch('<%= api.url("read_rulelist") %>?type=gfw')
			.then(response => response.text())
			.then(data => {
				var total_lines = data.split("\n").filter(line => line.trim() !== "").length;
				var textarea = document.getElementById('gfw_textarea');
				textarea.innerHTML = data;
				//textarea.scrollTop = textarea.scrollHeight;
				var totalLinesLabel = document.getElementById('gfw_total_lines');
				totalLinesLabel.innerHTML = "<%= total_lines_text %> " + total_lines;
			})
	}

	function read_chn() {
		fetch('<%= api.url("read_rulelist") %>?type=chn')
			.then(response => response.text())
			.then(data => {
				var total_lines = data.split("\n").filter(line => line.trim() !== "").length;
				var textarea = document.getElementById('chn_textarea');
				textarea.innerHTML = data;
				//textarea.scrollTop = textarea.scrollHeight;
				var totalLinesLabel = document.getElementById('chn_total_lines');
				totalLinesLabel.innerHTML = "<%= total_lines_text %> " + total_lines;
			})
	}

	function read_chnroute() {
		fetch('<%= api.url("read_rulelist") %>?type=chnroute')
			.then(response => response.text())
			.then(data => {
				var total_lines = data.split("\n").filter(line => line.trim() !== "").length;
				var textarea = document.getElementById('chnroute_textarea');
				textarea.innerHTML = data;
				//textarea.scrollTop = textarea.scrollHeight;
				var totalLinesLabel = document.getElementById('chnroute_total_lines');
				totalLinesLabel.innerHTML = "<%= total_lines_text %> " + total_lines;
			})
	}
	//]]>
</script>
