<%
local api = require "luci.passwall.api"
-%>
<script type="text/javascript">
	//<![CDATA[
	function clearlog(btn) {
		XHR.get('<%=api.url("clear_log")%>', null,
			function(x, data) {
				if(x && x.status == 200) {
					var log_textarea = document.getElementById('log_textarea');
					log_textarea.innerHTML = "";
					log_textarea.scrollTop = log_textarea.scrollHeight;
				}
			}
		);
	}

	XHR.poll(5, '<%=api.url("get_log")%>', null,
		function(x, data) {
			if(x && x.status == 200) {
				var log_textarea = document.getElementById('log_textarea');
				log_textarea.innerHTML = x.responseText;
			}
		}
	);
	//]]>
</script>
<fieldset class="cbi-section" id="_log_fieldset">
	<input class="btn cbi-button cbi-button-remove" type="button" onclick="clearlog()" value="<%:Clear logs%>" />
	<textarea id="log_textarea" class="cbi-input-textarea" style="width: 100%;margin-top: 10px;" data-update="change" rows="40" wrap="off" readonly="readonly"></textarea>
</fieldset>
