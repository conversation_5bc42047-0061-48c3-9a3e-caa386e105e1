#!/bin/bash

# XQNetwork 安装脚本
# 基于 openwrt-passwall 改造，专门用于对接面板 API

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用 root 权限运行此脚本"
        exit 1
    fi
}

# 检查 OpenWrt 环境
check_openwrt() {
    if [ ! -f "/etc/openwrt_release" ]; then
        print_error "此脚本仅适用于 OpenWrt 系统"
        exit 1
    fi
    
    print_info "检测到 OpenWrt 系统"
    cat /etc/openwrt_release
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖包..."
    
    local deps=(
        "curl"
        "lua"
        "luci-lib-jsonc"
        "coreutils"
        "coreutils-base64"
    )
    
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! opkg list-installed | grep -q "^$dep "; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_warning "缺少以下依赖包: ${missing_deps[*]}"
        print_info "正在安装依赖包..."
        
        opkg update
        for dep in "${missing_deps[@]}"; do
            print_info "安装 $dep..."
            opkg install "$dep" || print_warning "安装 $dep 失败，请手动安装"
        done
    else
        print_success "所有依赖包已安装"
    fi
}

# 备份原有配置
backup_config() {
    print_info "备份原有配置..."
    
    local backup_dir="/tmp/xqnetwork_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [ -f "/etc/config/xqnetwork" ]; then
        cp "/etc/config/xqnetwork" "$backup_dir/"
        print_info "已备份 xqnetwork 配置到 $backup_dir"
    fi
    
    echo "$backup_dir" > /tmp/xqnetwork_backup_path
}

# 安装 XQNetwork
install_xqnetwork() {
    print_info "安装 XQNetwork..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local src_dir="$script_dir/luci-app-xqnetwork"
    
    if [ ! -d "$src_dir" ]; then
        print_error "找不到 luci-app-xqnetwork 目录"
        exit 1
    fi
    
    # 复制文件
    print_info "复制应用文件..."
    cp -r "$src_dir/luasrc"/* "/usr/lib/lua/luci/" 2>/dev/null || true
    cp -r "$src_dir/root"/* "/" 2>/dev/null || true
    cp -r "$src_dir/htdocs"/* "/www/" 2>/dev/null || true
    
    # 设置权限
    chmod +x /usr/share/xqnetwork_main/*.sh 2>/dev/null || true
    chmod +x /usr/share/xqnetwork_main/*.lua 2>/dev/null || true
    chmod +x /etc/init.d/xqnetwork 2>/dev/null || true
    
    # 创建默认配置
    if [ ! -f "/etc/config/xqnetwork" ]; then
        print_info "创建默认配置..."
        cp "/usr/share/xqnetwork_main/0_default_config" "/etc/config/xqnetwork"
    fi
    
    # 创建隐藏配置文件
    print_info "创建隐藏配置文件..."
    cat > /tmp/.xqn_cfg << 'EOF'
{
  "oss_config_url": "https://xxx.obs.cn-south-4.myhuaweicloud.com/host.json",
  "app_name": "XQNetwork",
  "version": "1.0.0"
}
EOF
    
    print_success "XQNetwork 安装完成"
}

# 重启相关服务
restart_services() {
    print_info "重启相关服务..."
    
    # 重启 LuCI
    /etc/init.d/uhttpd restart
    
    # 清除 LuCI 缓存
    rm -rf /tmp/luci-*
    
    print_success "服务重启完成"
}

# 显示安装完成信息
show_completion_info() {
    print_success "XQNetwork 安装完成！"
    echo
    print_info "使用说明："
    echo "1. 在浏览器中访问路由器管理界面"
    echo "2. 在菜单中找到 'XQNetwork'"
    echo "3. 进入 'XQNetwork Login' 页面"
    echo "4. 填写邮箱和密码"
    echo "5. 同步节点并开始使用"
    echo
    print_info "特性说明："
    echo "- 自动隐藏节点敏感信息（IP、域名等）"
    echo "- 实时过滤日志中的敏感信息"
    echo "- 简化界面，专注核心功能"
    echo "- 禁用节点编辑、分享、导入导出功能"
    echo "- 统一错误提示：链接超时请联系管理员"
    echo
    
    if [ -f "/tmp/xqnetwork_backup_path" ]; then
        local backup_path=$(cat /tmp/xqnetwork_backup_path)
        print_info "配置备份位置: $backup_path"
        rm -f /tmp/xqnetwork_backup_path
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "       XQNetwork 安装脚本"
    echo "   基于 openwrt-passwall 改造"
    echo "     专门用于对接面板 API"
    echo "========================================"
    echo
    
    check_root
    check_openwrt
    check_dependencies
    backup_config
    install_xqnetwork
    restart_services
    show_completion_info
    
    print_success "安装完成！请刷新浏览器页面。"
}

# 运行主函数
main "$@"
