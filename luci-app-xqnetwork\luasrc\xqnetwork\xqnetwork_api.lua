module("luci.xqnetwork.xqnetwork_api", package.seeall)

local api = require "luci.xqnetwork.api"
local jsonc = require "luci.jsonc"
local sys = require "luci.sys"
local uci = require "luci.model.uci".cursor()
local fs = require "nixio.fs"

local appname = "xqnetwork"

-- XQNetwork API 配置
local XQNETWORK_CONFIG = {
    api_urls = {},
    email = "",
    password = "",
    token = "",
    token_expire = 0
}

-- 统一错误消息
local ERROR_MESSAGE = "链接超时请联系管理员"

-- 获取隐藏的配置文件路径
local function get_hidden_config_path()
    -- 使用隐藏的配置文件路径，用户看不到
    return "/tmp/.xqn_cfg"
end

-- 获取 OSS 配置文件地址
local function get_oss_config_url()
    local config_path = get_hidden_config_path()
    if fs.access(config_path) then
        local content = fs.readfile(config_path)
        if content then
            local config = jsonc.parse(content)
            if config and config.oss_config_url then
                return config.oss_config_url
            end
        end
    end
    
    -- 创建默认隐藏配置
    local default_config = {
        oss_config_url = "https://xxx.obs.cn-south-4.myhuaweicloud.com/host.json",
        app_name = "XQNetwork",
        version = "1.0.0"
    }
    
    fs.writefile(config_path, jsonc.stringify(default_config))
    return default_config.oss_config_url
end

-- 日志函数（不输出敏感信息）
local function log(msg)
    -- 只记录基本信息，不暴露 API 地址等敏感信息
    if msg:match("http") or msg:match("api") or msg:match("url") then
        api.log("网络请求处理中...", "XQNetwork")
    else
        api.log(msg, "XQNetwork")
    end
end

-- 获取 API 地址列表
local function get_api_urls()
    if #XQNETWORK_CONFIG.api_urls > 0 then
        return XQNETWORK_CONFIG.api_urls
    end
    
    log("获取配置信息...")
    
    local oss_url = get_oss_config_url()
    local curl_args = {"-skfL", "--connect-timeout 5", "--retry 1"}
    local return_code, result = api.curl_base(oss_url, nil, curl_args)
    
    if return_code == 0 and result then
        -- 解码 base64 内容
        local success, decoded = pcall(function()
            return api.bin.b64decode(result)
        end)
        
        if success and decoded then
            local config_data = jsonc.parse(decoded)
            if config_data and config_data.url and type(config_data.url) == "table" then
                XQNETWORK_CONFIG.api_urls = config_data.url
                log("配置获取成功")
                return XQNETWORK_CONFIG.api_urls
            end
        end
    end
    
    log("使用默认配置")
    return {}
end

-- 获取配置
local function get_config()
    XQNETWORK_CONFIG.email = uci:get(appname, "@xqnetwork[0]", "email") or ""
    XQNETWORK_CONFIG.password = uci:get(appname, "@xqnetwork[0]", "password") or ""
    XQNETWORK_CONFIG.token = uci:get(appname, "@xqnetwork[0]", "token") or ""
    XQNETWORK_CONFIG.token_expire = tonumber(uci:get(appname, "@xqnetwork[0]", "token_expire") or "0")
end

-- 保存配置
local function save_config()
    uci:set(appname, "@xqnetwork[0]", "email", XQNETWORK_CONFIG.email)
    uci:set(appname, "@xqnetwork[0]", "password", XQNETWORK_CONFIG.password)
    uci:set(appname, "@xqnetwork[0]", "token", XQNETWORK_CONFIG.token)
    uci:set(appname, "@xqnetwork[0]", "token_expire", tostring(XQNETWORK_CONFIG.token_expire))
    uci:commit(appname)
end

-- HTTP 请求函数（支持多个 API 地址重试，统一错误处理）
local function http_request(endpoint, method, data, headers)
    method = method or "GET"
    headers = headers or {}
    
    -- 添加自定义请求头
    headers["User-Agent"] = "xqnetworkpasswall"
    
    local api_urls = get_api_urls()
    
    if #api_urls == 0 then
        return nil, ERROR_MESSAGE
    end
    
    -- 尝试每个 API 地址
    for i, base_url in ipairs(api_urls) do
        local url = base_url .. endpoint
        log("处理请求...")
        
        local curl_args = {"-skfL", "--connect-timeout 5", "--retry 1"}
        
        -- 添加请求头
        for k, v in pairs(headers) do
            table.insert(curl_args, "-H")
            table.insert(curl_args, k .. ": " .. v)
        end
        
        -- 添加请求方法和数据
        if method == "POST" then
            table.insert(curl_args, "-X POST")
            if data then
                table.insert(curl_args, "-d")
                table.insert(curl_args, data)
                table.insert(curl_args, "-H")
                table.insert(curl_args, "Content-Type: application/json")
            end
        end
        
        local return_code, result = api.curl_base(url, nil, curl_args)
        
        if return_code == 0 and result then
            local json_data = jsonc.parse(result)
            if json_data then
                log("请求成功")
                return json_data, nil
            end
        end
        
        log("请求失败，尝试下一个地址...")
    end
    
    log("所有请求都失败")
    return nil, ERROR_MESSAGE
end

-- 检查 token 是否有效
local function is_token_valid()
    if not XQNETWORK_CONFIG.token or XQNETWORK_CONFIG.token == "" then
        return false
    end
    
    local current_time = os.time()
    if XQNETWORK_CONFIG.token_expire > 0 and current_time >= XQNETWORK_CONFIG.token_expire then
        return false
    end
    
    return true
end

-- 登录到 XQNetwork
function login(email, password)
    if not email or not password then
        return false, "缺少必要的登录参数"
    end
    
    local login_data = jsonc.stringify({
        email = email,
        password = password
    })
    
    log("尝试登录...")
    
    local response, error_msg = http_request("/api/v1/passport/auth/login", "POST", login_data)
    
    if response and response.data then
        XQNETWORK_CONFIG.email = email
        XQNETWORK_CONFIG.password = password
        XQNETWORK_CONFIG.token = response.data
        -- 设置 token 过期时间为 24 小时后
        XQNETWORK_CONFIG.token_expire = os.time() + 86400
        
        save_config()
        log("登录成功")
        return true, "登录成功"
    else
        log("登录失败")
        return false, error_msg or ERROR_MESSAGE
    end
end

-- 获取用户信息
function get_user_info()
    get_config()
    
    if not is_token_valid() then
        return nil, "Token 无效或已过期，请重新登录"
    end
    
    local headers = {
        ["Authorization"] = XQNETWORK_CONFIG.token
    }
    
    local response, error_msg = http_request("/api/v1/user/info", "GET", nil, headers)
    
    if response and response.data then
        return response.data, "获取用户信息成功"
    else
        return nil, error_msg or ERROR_MESSAGE
    end
end

-- 获取订阅链接
function get_subscribe_url()
    get_config()
    
    if not is_token_valid() then
        return nil, "Token 无效或已过期，请重新登录"
    end
    
    local headers = {
        ["Authorization"] = XQNETWORK_CONFIG.token
    }
    
    local response, error_msg = http_request("/api/v1/user/getSubscribe", "GET", nil, headers)
    
    if response and response.data and response.data.subscribe_url then
        return response.data.subscribe_url, "获取订阅链接成功"
    else
        return nil, error_msg or ERROR_MESSAGE
    end
end

-- 获取节点列表（通过订阅）
function get_nodes()
    local subscribe_url, msg = get_subscribe_url()
    if not subscribe_url then
        return nil, msg
    end

    log("获取节点列表...")

    local curl_args = {"-skfL", "--connect-timeout 5", "--retry 1"}
    local return_code, result = api.curl_base(subscribe_url, nil, curl_args)

    if return_code == 0 and result then
        -- 解析订阅内容，通常是 base64 编码的节点信息
        local decoded_content

        -- 尝试 base64 解码
        local success, decoded = pcall(function()
            return api.bin.b64decode(result)
        end)

        if success and decoded then
            decoded_content = decoded
        else
            -- 如果不是 base64 编码，直接返回原始内容
            decoded_content = result
        end

        return decoded_content, "获取节点列表成功"
    else
        return nil, ERROR_MESSAGE
    end
end

-- 隐藏节点敏感信息的函数
function hide_node_info(node_data)
    if not node_data then
        return node_data
    end

    -- 隐藏 IP 地址
    if node_data.address then
        -- 将 IP 地址替换为星号
        if api.datatypes.ipaddr(node_data.address) then
            local parts = {}
            for part in node_data.address:gmatch("[^%.]+") do
                table.insert(parts, part)
            end
            if #parts == 4 then
                node_data.address = parts[1] .. ".***.***.***"
            end
        elseif api.datatypes.ip6addr(node_data.address) then
            -- IPv6 地址隐藏
            local parts = {}
            for part in node_data.address:gmatch("[^:]+") do
                table.insert(parts, part)
            end
            if #parts >= 2 then
                node_data.address = parts[1] .. ":***::***"
            end
        else
            -- 域名隐藏
            local domain_parts = {}
            for part in node_data.address:gmatch("[^%.]+") do
                table.insert(domain_parts, part)
            end
            if #domain_parts >= 2 then
                node_data.address = "***." .. domain_parts[#domain_parts]
            end
        end
    end

    -- 隐藏其他敏感信息
    if node_data.server then
        node_data.server = node_data.address
    end

    return node_data
end

-- 检查登录状态
function check_login_status()
    get_config()

    if not is_token_valid() then
        return false, "Token 无效或已过期"
    end

    -- 尝试获取用户信息来验证登录状态
    local user_info, msg = get_user_info()
    if user_info then
        return true, "已登录"
    else
        return false, msg
    end
end

-- 登出
function logout()
    XQNETWORK_CONFIG.token = ""
    XQNETWORK_CONFIG.token_expire = 0
    save_config()
    log("已登出")
    return true, "登出成功"
end
