<%
local api = require "luci.passwall.api"
local console_port = api.uci_get_type("@global_haproxy[0]", "console_port", "")
-%>
<p id="_status"></p>

<script type="text/javascript">//<![CDATA[
	XHR.poll(3, '<%=api.url("haproxy_status")%>', null,
		function(x, result) {
			if (x && x.status == 200) {
				var _status = document.getElementById('_status');
				if (_status) {
					if (result) {
						_status.innerHTML = '<input type="button" class="btn cbi-button cbi-button-apply" value="<%:Enter interface%>" onclick="openwebui()" />';
					} else {
						_status.innerHTML = '';
					}
				}
			}
		});

	function openwebui(){
		var url = window.location.hostname + ":<%=console_port%>";
		window.open('http://' + url, 'target', '');
	}
//]]></script>
