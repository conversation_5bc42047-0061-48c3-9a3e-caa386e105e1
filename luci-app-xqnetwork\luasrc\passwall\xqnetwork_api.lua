module("luci.xqnetwork.xqnetwork_api", package.seeall)

local api = require "luci.xqnetwork.api"
local jsonc = require "luci.jsonc"
local sys = require "luci.sys"
local uci = require "luci.model.uci".cursor()
local fs = require "nixio.fs"

local appname = "xqnetwork"

-- XQNetwork API 配置
local XQNETWORK_CONFIG = {
    api_urls = {},
    email = "",
    password = "",
    token = "",
    token_expire = 0
}

-- 获取 OSS 配置文件地址
local function get_oss_config_url()
    local config_file = "/usr/share/xqnetwork/config.json"
    if fs.access(config_file) then
        local content = fs.readfile(config_file)
        if content then
            local config = jsonc.parse(content)
            if config and config.oss_config_url then
                return config.oss_config_url
            end
        end
    end
    -- 默认地址
    return "https://xxx.obs.cn-south-4.myhuaweicloud.com/host.json"
end

-- 日志函数
local function log(msg)
    api.log(msg, "XQNetwork")
end

-- 获取 API 地址列表
local function get_api_urls()
    if #XQNETWORK_CONFIG.api_urls > 0 then
        return XQNETWORK_CONFIG.api_urls
    end

    log("从 OSS 获取 API 地址列表...")

    local oss_url = get_oss_config_url()
    local curl_args = {"-skfL", "--connect-timeout 10", "--retry 2"}
    local return_code, result = api.curl_base(oss_url, nil, curl_args)

    if return_code == 0 and result then
        -- 解码 base64 内容
        local success, decoded = pcall(function()
            return api.bin.b64decode(result)
        end)

        if success and decoded then
            local config_data = jsonc.parse(decoded)
            if config_data and config_data.url and type(config_data.url) == "table" then
                XQNETWORK_CONFIG.api_urls = config_data.url
                log("成功获取 " .. #XQNETWORK_CONFIG.api_urls .. " 个 API 地址")
                return XQNETWORK_CONFIG.api_urls
            end
        end
    end

    log("获取 API 地址失败，使用默认地址")
    return {}
end

-- 获取配置
local function get_config()
    XQNETWORK_CONFIG.email = uci:get(appname, "@xqnetwork[0]", "email") or ""
    XQNETWORK_CONFIG.password = uci:get(appname, "@xqnetwork[0]", "password") or ""
    XQNETWORK_CONFIG.token = uci:get(appname, "@xqnetwork[0]", "token") or ""
    XQNETWORK_CONFIG.token_expire = tonumber(uci:get(appname, "@xqnetwork[0]", "token_expire") or "0")
end

-- 保存配置
local function save_config()
    uci:set(appname, "@xqnetwork[0]", "email", XQNETWORK_CONFIG.email)
    uci:set(appname, "@xqnetwork[0]", "password", XQNETWORK_CONFIG.password)
    uci:set(appname, "@xqnetwork[0]", "token", XQNETWORK_CONFIG.token)
    uci:set(appname, "@xqnetwork[0]", "token_expire", tostring(XQNETWORK_CONFIG.token_expire))
    uci:commit(appname)
end

-- HTTP 请求函数（支持多个 API 地址重试）
local function http_request(endpoint, method, data, headers)
    method = method or "GET"
    headers = headers or {}

    -- 添加自定义请求头
    headers["User-Agent"] = "xqnetworkpasswall"

    local api_urls = get_api_urls()

    if #api_urls == 0 then
        log("没有可用的 API 地址")
        return nil
    end

    -- 尝试每个 API 地址
    for i, base_url in ipairs(api_urls) do
        local url = base_url .. endpoint
        log("尝试请求: " .. url .. " (第 " .. i .. " 个地址)")

        local curl_args = {"-skfL", "--connect-timeout 10", "--retry 1"}

        -- 添加请求头
        for k, v in pairs(headers) do
            table.insert(curl_args, "-H")
            table.insert(curl_args, k .. ": " .. v)
        end

        -- 添加请求方法和数据
        if method == "POST" then
            table.insert(curl_args, "-X POST")
            if data then
                table.insert(curl_args, "-d")
                table.insert(curl_args, data)
                table.insert(curl_args, "-H")
                table.insert(curl_args, "Content-Type: application/json")
            end
        end

        local return_code, result = api.curl_base(url, nil, curl_args)

        if return_code == 0 and result then
            local json_data = jsonc.parse(result)
            if json_data then
                log("请求成功: " .. url)
                return json_data
            end
        end

        log("请求失败: " .. url .. " 返回码: " .. tostring(return_code))

        -- 如果不是最后一个地址，继续尝试下一个
        if i < #api_urls then
            log("尝试下一个 API 地址...")
        end
    end

    log("所有 API 地址都请求失败")
    return nil
end

-- 检查 token 是否有效
local function is_token_valid()
    if not XQNETWORK_CONFIG.token or XQNETWORK_CONFIG.token == "" then
        return false
    end

    local current_time = os.time()
    if XQNETWORK_CONFIG.token_expire > 0 and current_time >= XQNETWORK_CONFIG.token_expire then
        return false
    end

    return true
end

-- 登录到 XQNetwork
function login(email, password)
    if not email or not password then
        return false, "缺少必要的登录参数"
    end

    local login_data = jsonc.stringify({
        email = email,
        password = password
    })

    log("尝试登录到 XQNetwork...")

    local response = http_request("/api/v1/passport/auth/login", "POST", login_data)

    if response and response.data then
        XQNETWORK_CONFIG.email = email
        XQNETWORK_CONFIG.password = password
        XQNETWORK_CONFIG.token = response.data
        -- 设置 token 过期时间为 24 小时后
        XQNETWORK_CONFIG.token_expire = os.time() + 86400

        save_config()
        log("登录成功")
        return true, "登录成功"
    else
        local error_msg = "登录失败"
        if response and response.message then
            error_msg = error_msg .. ": " .. response.message
        end
        log(error_msg)
        return false, error_msg
    end
end

-- 获取用户信息
function get_user_info()
    get_config()

    if not is_token_valid() then
        return nil, "Token 无效或已过期，请重新登录"
    end

    local headers = {
        ["Authorization"] = XQNETWORK_CONFIG.token
    }

    local response = http_request("/api/v1/user/info", "GET", nil, headers)

    if response and response.data then
        return response.data, "获取用户信息成功"
    else
        local error_msg = "获取用户信息失败"
        if response and response.message then
            error_msg = error_msg .. ": " .. response.message
        end
        return nil, error_msg
    end
end

-- 获取订阅链接
function get_subscribe_url()
    get_config()

    if not is_token_valid() then
        return nil, "Token 无效或已过期，请重新登录"
    end

    local headers = {
        ["Authorization"] = XQNETWORK_CONFIG.token
    }

    local response = http_request("/api/v1/user/getSubscribe", "GET", nil, headers)

    if response and response.data and response.data.subscribe_url then
        return response.data.subscribe_url, "获取订阅链接成功"
    else
        local error_msg = "获取订阅链接失败"
        if response and response.message then
            error_msg = error_msg .. ": " .. response.message
        end
        return nil, error_msg
    end
end

-- 获取节点列表（通过订阅）
function get_nodes()
    local subscribe_url, msg = get_subscribe_url()
    if not subscribe_url then
        return nil, msg
    end

    log("获取节点列表: " .. subscribe_url)

    local curl_args = {"-skfL", "--connect-timeout 10", "--retry 2"}
    local return_code, result = api.curl_base(subscribe_url, nil, curl_args)

    if return_code == 0 and result then
        -- 解析订阅内容，通常是 base64 编码的节点信息
        local decoded_content

        -- 尝试 base64 解码
        local success, decoded = pcall(function()
            return api.bin.b64decode(result)
        end)

        if success and decoded then
            decoded_content = decoded
        else
            -- 如果不是 base64 编码，直接返回原始内容
            decoded_content = result
        end

        return decoded_content, "获取节点列表成功"
    else
        return nil, "获取节点列表失败"
    end
end

-- 隐藏节点敏感信息的函数
function hide_node_info(node_data)
    if not node_data then
        return node_data
    end

    -- 隐藏 IP 地址
    if node_data.address then
        -- 将 IP 地址替换为星号
        if api.datatypes.ipaddr(node_data.address) then
            local parts = {}
            for part in node_data.address:gmatch("[^%.]+") do
                table.insert(parts, part)
            end
            if #parts == 4 then
                node_data.address = parts[1] .. ".***.***.***"
            end
        elseif api.datatypes.ip6addr(node_data.address) then
            -- IPv6 地址隐藏
            local parts = {}
            for part in node_data.address:gmatch("[^:]+") do
                table.insert(parts, part)
            end
            if #parts >= 2 then
                node_data.address = parts[1] .. ":***::***"
            end
        else
            -- 域名隐藏
            local domain_parts = {}
            for part in node_data.address:gmatch("[^%.]+") do
                table.insert(domain_parts, part)
            end
            if #domain_parts >= 2 then
                node_data.address = "***." .. domain_parts[#domain_parts]
            end
        end
    end

    -- 隐藏其他敏感信息
    if node_data.server then
        node_data.server = node_data.address
    end

    return node_data
end

-- 检查登录状态
function check_login_status()
    get_config()

    if not is_token_valid() then
        return false, "Token 无效或已过期"
    end

    -- 尝试获取用户信息来验证登录状态
    local user_info, msg = get_user_info()
    if user_info then
        return true, "已登录"
    else
        return false, msg
    end
end

-- 登出
function logout()
    XQNETWORK_CONFIG.token = ""
    XQNETWORK_CONFIG.token_expire = 0
    save_config()
    log("已登出")
    return true, "登出成功"
end
