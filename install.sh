﻿#!/bin/bash

# XQNetwork 瀹夎鑴氭湰
# 鍩轰簬 openwrt-xqnetwork 鏀归€狅紝涓撻棬鐢ㄤ簬瀵规帴 V2Board

set -e

# 棰滆壊瀹氫箟
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 鎵撳嵃甯﹂鑹茬殑娑堟伅
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 妫€鏌ユ槸鍚︿负 root 鐢ㄦ埛
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "璇蜂娇鐢?root 鏉冮檺杩愯姝よ剼鏈?
        exit 1
    fi
}

# 妫€鏌?OpenWrt 鐜
check_openwrt() {
    if [ ! -f "/etc/openwrt_release" ]; then
        print_error "姝よ剼鏈粎閫傜敤浜?OpenWrt 绯荤粺"
        exit 1
    fi
    
    print_info "妫€娴嬪埌 OpenWrt 绯荤粺"
    cat /etc/openwrt_release
}

# 妫€鏌ヤ緷璧?check_dependencies() {
    print_info "妫€鏌ヤ緷璧栧寘..."
    
    local deps=(
        "curl"
        "lua"
        "luci-lib-jsonc"
        "coreutils"
        "coreutils-base64"
    )
    
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! opkg list-installed | grep -q "^$dep "; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_warning "缂哄皯浠ヤ笅渚濊禆鍖? ${missing_deps[*]}"
        print_info "姝ｅ湪瀹夎渚濊禆鍖?.."
        
        opkg update
        for dep in "${missing_deps[@]}"; do
            print_info "瀹夎 $dep..."
            opkg install "$dep" || print_warning "瀹夎 $dep 澶辫触锛岃鎵嬪姩瀹夎"
        done
    else
        print_success "鎵€鏈変緷璧栧寘宸插畨瑁?
    fi
}

# 澶囦唤鍘熸湁閰嶇疆
backup_config() {
    print_info "澶囦唤鍘熸湁閰嶇疆..."
    
    local backup_dir="/tmp/xqnetwork_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [ -f "/etc/config/xqnetwork" ]; then
        cp "/etc/config/xqnetwork" "$backup_dir/"
        print_info "宸插浠?xqnetwork 閰嶇疆鍒?$backup_dir"
    fi
    
    echo "$backup_dir" > /tmp/xqnetwork_backup_path
}

# 瀹夎 XQNetwork
install_xqnetwork() {
    print_info "瀹夎 XQNetwork..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local src_dir="$script_dir/luci-app-xqnetwork"
    
    if [ ! -d "$src_dir" ]; then
        print_error "鎵句笉鍒?luci-app-xqnetwork 鐩綍"
        exit 1
    fi
    
    # 澶嶅埗鏂囦欢
    print_info "澶嶅埗搴旂敤鏂囦欢..."
    cp -r "$src_dir/luasrc"/* "/usr/lib/lua/luci/" 2>/dev/null || true
    cp -r "$src_dir/root"/* "/" 2>/dev/null || true
    cp -r "$src_dir/htdocs"/* "/www/" 2>/dev/null || true
    
    # 璁剧疆鏉冮檺
    chmod +x /usr/share/xqnetwork/*.sh 2>/dev/null || true
    chmod +x /usr/share/xqnetwork/*.lua 2>/dev/null || true
    chmod +x /etc/init.d/xqnetwork 2>/dev/null || true
    
    # 鍒涘缓榛樿閰嶇疆
    if [ ! -f "/etc/config/xqnetwork" ]; then
        print_info "鍒涘缓榛樿閰嶇疆..."
        cp "/usr/share/xqnetwork/0_default_config" "/etc/config/xqnetwork"
    fi
    
    print_success "XQNetwork 瀹夎瀹屾垚"
}

# 閲嶅惎鐩稿叧鏈嶅姟
restart_services() {
    print_info "閲嶅惎鐩稿叧鏈嶅姟..."
    
    # 閲嶅惎 LuCI
    /etc/init.d/uhttpd restart
    
    # 娓呴櫎 LuCI 缂撳瓨
    rm -rf /tmp/luci-*
    
    print_success "鏈嶅姟閲嶅惎瀹屾垚"
}

# 鏄剧ず瀹夎瀹屾垚淇℃伅
show_completion_info() {
    print_success "XQNetwork 瀹夎瀹屾垚锛?
    echo
    print_info "浣跨敤璇存槑锛?
    echo "1. 鍦ㄦ祻瑙堝櫒涓闂矾鐢卞櫒绠＄悊鐣岄潰"
    echo "2. 鍦ㄨ彍鍗曚腑鎵惧埌 'XQNetwork'"
    echo "3. 杩涘叆 'V2Board Login' 椤甸潰"
    echo "4. 濉啓 V2Board 闈㈡澘淇℃伅骞剁櫥褰?
    echo "5. 鍚屾鑺傜偣骞跺紑濮嬩娇鐢?
    echo
    print_info "鐗规€ц鏄庯細"
    echo "- 鑷姩闅愯棌鑺傜偣鏁忔劅淇℃伅锛圛P銆佸煙鍚嶇瓑锛?
    echo "- 瀹炴椂杩囨护鏃ュ織涓殑鏁忔劅淇℃伅"
    echo "- 绠€鍖栫晫闈紝涓撴敞鏍稿績鍔熻兘"
    echo
    
    if [ -f "/tmp/xqnetwork_backup_path" ]; then
        local backup_path=$(cat /tmp/xqnetwork_backup_path)
        print_info "閰嶇疆澶囦唤浣嶇疆: $backup_path"
        rm -f /tmp/xqnetwork_backup_path
    fi
}

# 涓诲嚱鏁?main() {
    echo "========================================"
    echo "       XQNetwork 瀹夎鑴氭湰"
    echo "   鍩轰簬 openwrt-xqnetwork 鏀归€?
    echo "     涓撻棬鐢ㄤ簬瀵规帴 V2Board"
    echo "========================================"
    echo
    
    check_root
    check_openwrt
    check_dependencies
    backup_config
    install_xqnetwork
    restart_services
    show_completion_info
    
    print_success "瀹夎瀹屾垚锛佽鍒锋柊娴忚鍣ㄩ〉闈€?
}

# 杩愯涓诲嚱鏁?main "$@"

