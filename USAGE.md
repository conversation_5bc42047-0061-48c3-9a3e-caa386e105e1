﻿# XQNetwork 浣跨敤璇存槑

## 姒傝堪

XQNetwork 鏄熀浜?openwrt-xqnetwork 鏀归€犵殑涓撶敤搴旂敤锛屼笓闂ㄧ敤浜庡鎺ラ潰鏉?API銆傚畠鎻愪緵浜嗙畝鍖栫殑鐣岄潰鍜屽寮虹殑闅愮淇濇姢鍔熻兘銆?
## 涓昏鐗规€?
### 馃攼 API 闆嗘垚
- 鐩存帴鐧诲綍闈㈡澘
- 鑷姩鑾峰彇璁㈤槄閾炬帴
- 瀹炴椂鏄剧ず鐢ㄦ埛淇℃伅锛堟祦閲忋€佸埌鏈熸椂闂寸瓑锛?- 澶?API 鍦板潃鑷姩閲嶈瘯鏈哄埗

### 馃洝锔?闅愮淇濇姢
- 鑷姩闅愯棌鑺傜偣鐪熷疄 IP 鍦板潃
- 闅愯棌鑺傜偣鍩熷悕淇℃伅
- 瀹炴椂杩囨护鏃ュ織涓殑鏁忔劅淇℃伅

### 馃幆 绠€鍖栫晫闈?- 鍘婚櫎涓嶅繀瑕佺殑鍔熻兘妯″潡
- 涓撴敞浜庢牳蹇冧唬鐞嗗姛鑳?- 浼樺寲鐢ㄦ埛浣撻獙

## 瀹夎鏂规硶

### 鏂规硶涓€锛氫娇鐢ㄥ畨瑁呰剼鏈紙鎺ㄨ崘锛?
1. 灏嗘暣涓」鐩笂浼犲埌璺敱鍣?2. 杩愯瀹夎鑴氭湰锛?```bash
chmod +x install.sh
./install.sh
```

### 鏂规硶浜岋細鎵嬪姩瀹夎

1. 澶嶅埗鏂囦欢鍒板搴旂洰褰曪細
```bash
cp -r luci-app-xqnetwork/luasrc/* /usr/lib/lua/luci/
cp -r luci-app-xqnetwork/root/* /
cp -r luci-app-xqnetwork/htdocs/* /www/
```

2. 璁剧疆鏉冮檺锛?```bash
chmod +x /usr/share/xqnetwork/*.sh
chmod +x /usr/share/xqnetwork/*.lua
chmod +x /etc/init.d/xqnetwork
```

3. 鍒涘缓榛樿閰嶇疆锛?```bash
cp /usr/share/xqnetwork/0_default_config /etc/config/xqnetwork
```

4. 閲嶅惎鏈嶅姟锛?```bash
/etc/init.d/uhttpd restart
```

## 浣跨敤姝ラ

### 1. 璁块棶绠＄悊鐣岄潰

鍦ㄦ祻瑙堝櫒涓闂矾鐢卞櫒绠＄悊鐣岄潰锛屽湪鑿滃崟涓壘鍒?**XQNetwork**銆?
### 2. XQNetwork 鐧诲綍

1. 鐐瑰嚮 **XQNetwork Login** 鑿滃崟
2. 濉啓浠ヤ笅淇℃伅锛?   - **Email**: 鎮ㄧ殑璐︽埛閭
   - **Password**: 鎮ㄧ殑璐︽埛瀵嗙爜
3. 鐐瑰嚮 **鐧诲綍** 鎸夐挳

娉ㄦ剰锛欰PI 鍦板潃浼氳嚜鍔ㄤ粠閰嶇疆鏂囦欢鑾峰彇锛屾棤闇€鎵嬪姩濉啓銆?
### 3. 鏌ョ湅鐢ㄦ埛淇℃伅

鐧诲綍鎴愬姛鍚庯紝椤甸潰浼氭樉绀猴細
- 鐧诲綍鐘舵€?- 鐢ㄦ埛閭
- 褰撳墠濂楅
- 鎬绘祦閲?宸茬敤娴侀噺/鍓╀綑娴侀噺
- 濂楅鍒版湡鏃堕棿

### 4. 鍚屾鑺傜偣

1. 鍦?XQNetwork Login 椤甸潰鐐瑰嚮 **鍚屾鑺傜偣** 鎸夐挳
2. 绛夊緟鍚屾瀹屾垚
3. 鍓嶅線 **Node List** 鏌ョ湅鑺傜偣鍒楄〃

### 5. 浣跨敤鑺傜偣

1. 鍦?**Node List** 涓€夋嫨瑕佷娇鐢ㄧ殑鑺傜偣
2. 鍓嶅線 **Basic Settings** 閰嶇疆浠ｇ悊瑙勫垯
3. 鍚敤鏈嶅姟寮€濮嬩娇鐢?
## 鍔熻兘璇存槑

### 鑺傜偣淇℃伅闅愯棌

瀵逛簬浠?XQNetwork 鍚屾鐨勮妭鐐癸紝绯荤粺浼氳嚜鍔ㄩ殣钘忔晱鎰熶俊鎭細

- **IP 鍦板潃闅愯棌**: `*************` 鈫?`192.***.***.***`
- **IPv6 鍦板潃闅愯棌**: `2001:db8::1` 鈫?`2001:***::***`
- **鍩熷悕闅愯棌**: `node.example.com` 鈫?`***.com`

### 鏃ュ織杩囨护

绯荤粺浼氭瘡 30 绉掕嚜鍔ㄨ繃婊や互涓嬫棩蹇楁枃浠朵腑鐨勬晱鎰熶俊鎭細
- `/tmp/log/xqnetwork.log`
- `/tmp/log/xqnetwork_server.log`
- ACL 鐩稿叧鏃ュ織
- SOCKS 浠ｇ悊鏃ュ織
- DNS 瑙ｆ瀽鏃ュ織

### 鐣岄潰绠€鍖?
涓轰簡涓撴敞浜庢牳蹇冨姛鑳斤紝浠ヤ笅鍔熻兘琚殣钘忥細
- 鏈嶅姟绔姛鑳?- 璐熻浇鍧囪　
- 搴旂敤鏇存柊
- 瑙勫垯绠＄悊
- 鍏朵粬楂樼骇璁剧疆

濡傞渶浣跨敤杩欎簺鍔熻兘锛屽彲浠ラ€氳繃鐩存帴璁块棶瀵瑰簲 URL 鏉ヤ娇鐢ㄣ€?
## 閰嶇疆鏂囦欢

### 涓婚厤缃枃浠?- `/etc/config/xqnetwork` - 涓昏閰嶇疆
- `/etc/config/xqnetwork_server` - 鏈嶅姟绔厤缃紙濡傛灉鍚敤锛?
### 瑙勫垯鏂囦欢
- `/usr/share/xqnetwork/rules/` - 鍚勭鍒嗘祦瑙勫垯

### 鏃ュ織鏂囦欢
- `/tmp/log/xqnetwork.log` - 涓绘棩蹇?- `/tmp/etc/xqnetwork/` - 杩愯鏃舵枃浠?
## 鏁呴殰鎺掗櫎

### 鐧诲綍澶辫触
1. 妫€鏌?V2Board 鍦板潃鏄惁姝ｇ‘锛堥渶瑕佸寘鍚?https://锛?2. 纭閭鍜屽瘑鐮佹纭?3. 妫€鏌ョ綉缁滆繛鎺?4. 鏌ョ湅鏃ュ織锛歚cat /tmp/log/xqnetwork.log`

### 鑺傜偣鍚屾澶辫触
1. 纭繚宸叉垚鍔熺櫥褰?V2Board
2. 妫€鏌?V2Board 闈㈡澘鏄惁鏈夊彲鐢ㄨ妭鐐?3. 鏌ョ湅璁㈤槄閾炬帴鏄惁鏈夋晥
4. 妫€鏌ョ綉缁滆繛鎺?
### 浠ｇ悊涓嶅伐浣?1. 妫€鏌ヨ妭鐐规槸鍚﹂€夋嫨姝ｇ‘
2. 纭鏈嶅姟鏄惁鍚姩锛歚/etc/init.d/xqnetwork status`
3. 鏌ョ湅闃茬伀澧欒鍒欐槸鍚︽纭?4. 妫€鏌?DNS 璁剧疆

### 鏌ョ湅鏃ュ織
```bash
# 鏌ョ湅涓绘棩蹇?cat /tmp/log/xqnetwork.log

# 鏌ョ湅瀹炴椂鏃ュ織
tail -f /tmp/log/xqnetwork.log

# 鏌ョ湅鏈嶅姟鐘舵€?/etc/init.d/xqnetwork status
```

## 鍗歌浇

浣跨敤鎻愪緵鐨勫嵏杞借剼鏈細
```bash
chmod +x uninstall.sh
./uninstall.sh
```

鎴栨墜鍔ㄥ垹闄ょ浉鍏虫枃浠跺拰閰嶇疆銆?
## 娉ㄦ剰浜嬮」

1. **鍏煎鎬?*: 鏈簲鐢ㄤ笓闂ㄩ拡瀵?V2Board 浼樺寲锛屽叾浠栭潰鏉垮彲鑳戒笉鍏煎
2. **闅愮淇濇姢**: 鑺傜偣淇℃伅闅愯棌浠呭 V2Board 鑺傜偣鐢熸晥
3. **澶囦唤**: 寤鸿瀹氭湡澶囦唤閰嶇疆鏂囦欢
4. **鏇存柊**: 寤鸿鍏虫敞椤圭洰鏇存柊浠ヨ幏寰楁渶鏂板姛鑳藉拰瀹夊叏淇
5. **缃戠粶**: 纭繚璺敱鍣ㄨ兘姝ｅ父璁块棶 V2Board 闈㈡澘

## 鎶€鏈敮鎸?
濡傞亣鍒伴棶棰橈紝璇凤細
1. 鏌ョ湅鏈枃妗ｇ殑鏁呴殰鎺掗櫎閮ㄥ垎
2. 妫€鏌ユ棩蹇楁枃浠惰幏鍙栬缁嗛敊璇俊鎭?3. 纭 V2Board 闈㈡澘宸ヤ綔姝ｅ父
4. 妫€鏌ョ綉缁滆繛鎺ュ拰闃茬伀澧欒缃?
## 鏇存柊鏃ュ織

### v1.0.0
- 鍒濆鐗堟湰鍙戝竷
- 鍩轰簬 openwrt-xqnetwork 鏀归€?- 闆嗘垚 V2Board 鐧诲綍鍔熻兘
- 瀹炵幇鑺傜偣淇℃伅闅愯棌
- 娣诲姞鏃ュ織杩囨护鍔熻兘
- 绠€鍖栫敤鎴风晫闈?
