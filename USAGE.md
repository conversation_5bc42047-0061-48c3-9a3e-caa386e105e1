# XQNetwork 使用说明

## 概述

XQNetwork 是基于 openwrt-passwall 改造的专用应用，专门用于对接面板 API。它提供了简化的界面和增强的隐私保护功能。

## 主要特性

### 🔐 API 集成
- 直接登录面板
- 自动获取订阅链接
- 实时显示用户信息（流量、到期时间等）
- 多 API 地址自动重试机制

### 🛡️ 隐私保护
- 自动隐藏节点真实 IP 地址
- 隐藏节点域名信息
- 实时过滤日志中的敏感信息

### 🎯 简化界面
- 去除不必要的功能模块
- 专注于核心代理功能
- 优化用户体验

## 安装方法

### 方法一：使用安装脚本（推荐）

1. 将整个项目上传到路由器
2. 运行安装脚本：
```bash
chmod +x install.sh
./install.sh
```

### 方法二：手动安装

1. 复制文件到对应目录：
```bash
cp -r luci-app-xqnetwork/luasrc/* /usr/lib/lua/luci/
cp -r luci-app-xqnetwork/root/* /
cp -r luci-app-xqnetwork/htdocs/* /www/
```

2. 设置权限：
```bash
chmod +x /usr/share/passwall/*.sh
chmod +x /usr/share/passwall/*.lua
chmod +x /etc/init.d/passwall
```

3. 创建默认配置：
```bash
cp /usr/share/passwall/0_default_config /etc/config/passwall
```

4. 重启服务：
```bash
/etc/init.d/uhttpd restart
```

## 使用步骤

### 1. 访问管理界面

在浏览器中访问路由器管理界面，在菜单中找到 **XQNetwork**。

### 2. XQNetwork 登录

1. 点击 **XQNetwork Login** 菜单
2. 填写以下信息：
   - **Email**: 您的账户邮箱
   - **Password**: 您的账户密码
3. 点击 **登录** 按钮

注意：API 地址会自动从配置文件获取，无需手动填写。

### 3. 查看用户信息

登录成功后，页面会显示：
- 登录状态
- 用户邮箱
- 当前套餐
- 总流量/已用流量/剩余流量
- 套餐到期时间

### 4. 同步节点

1. 在 XQNetwork Login 页面点击 **同步节点** 按钮
2. 等待同步完成
3. 前往 **Node List** 查看节点列表

### 5. 使用节点

1. 在 **Node List** 中选择要使用的节点
2. 前往 **Basic Settings** 配置代理规则
3. 启用服务开始使用

## 功能说明

### 节点信息隐藏

对于从 XQNetwork 同步的节点，系统会自动隐藏敏感信息：

- **IP 地址隐藏**: `*************` → `192.***.***.***`
- **IPv6 地址隐藏**: `2001:db8::1` → `2001:***::***`
- **域名隐藏**: `node.example.com` → `***.com`

### 日志过滤

系统会每 30 秒自动过滤以下日志文件中的敏感信息：
- `/tmp/log/passwall.log`
- `/tmp/log/passwall_server.log`
- ACL 相关日志
- SOCKS 代理日志
- DNS 解析日志

### 界面简化

为了专注于核心功能，以下功能被隐藏：
- 服务端功能
- 负载均衡
- 应用更新
- 规则管理
- 其他高级设置

如需使用这些功能，可以通过直接访问对应 URL 来使用。

## 配置文件

### 主配置文件
- `/etc/config/passwall` - 主要配置
- `/etc/config/passwall_server` - 服务端配置（如果启用）

### 规则文件
- `/usr/share/passwall/rules/` - 各种分流规则

### 日志文件
- `/tmp/log/passwall.log` - 主日志
- `/tmp/etc/passwall/` - 运行时文件

## 故障排除

### 登录失败
1. 检查 V2Board 地址是否正确（需要包含 https://）
2. 确认邮箱和密码正确
3. 检查网络连接
4. 查看日志：`cat /tmp/log/passwall.log`

### 节点同步失败
1. 确保已成功登录 V2Board
2. 检查 V2Board 面板是否有可用节点
3. 查看订阅链接是否有效
4. 检查网络连接

### 代理不工作
1. 检查节点是否选择正确
2. 确认服务是否启动：`/etc/init.d/passwall status`
3. 查看防火墙规则是否正确
4. 检查 DNS 设置

### 查看日志
```bash
# 查看主日志
cat /tmp/log/passwall.log

# 查看实时日志
tail -f /tmp/log/passwall.log

# 查看服务状态
/etc/init.d/passwall status
```

## 卸载

使用提供的卸载脚本：
```bash
chmod +x uninstall.sh
./uninstall.sh
```

或手动删除相关文件和配置。

## 注意事项

1. **兼容性**: 本应用专门针对 V2Board 优化，其他面板可能不兼容
2. **隐私保护**: 节点信息隐藏仅对 V2Board 节点生效
3. **备份**: 建议定期备份配置文件
4. **更新**: 建议关注项目更新以获得最新功能和安全修复
5. **网络**: 确保路由器能正常访问 V2Board 面板

## 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 确认 V2Board 面板工作正常
4. 检查网络连接和防火墙设置

## 更新日志

### v1.0.0
- 初始版本发布
- 基于 openwrt-passwall 改造
- 集成 V2Board 登录功能
- 实现节点信息隐藏
- 添加日志过滤功能
- 简化用户界面
