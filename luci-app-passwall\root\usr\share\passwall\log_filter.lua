#!/usr/bin/lua

-- 日志过滤脚本，用于隐藏敏感的节点信息
local api = require "luci.passwall.api"
local uci = api.uci
local appname = "passwall"

-- 获取所有 V2Board 节点的敏感信息
local function get_v2board_sensitive_info()
    local sensitive_info = {}
    
    uci:foreach(appname, "nodes", function(node)
        if node.v2board_node == "1" then
            -- 收集需要隐藏的信息
            if node.address then
                table.insert(sensitive_info, node.address)
            end
            if node.server then
                table.insert(sensitive_info, node.server)
            end
            -- 如果有原始地址信息，也要隐藏
            if node.original_address then
                table.insert(sensitive_info, node.original_address)
            end
        end
    end)
    
    return sensitive_info
end

-- 过滤日志内容
local function filter_log_content(content)
    if not content or content == "" then
        return content
    end
    
    local sensitive_info = get_v2board_sensitive_info()
    local filtered_content = content
    
    -- 替换敏感信息
    for _, info in ipairs(sensitive_info) do
        if info and info ~= "" then
            -- 替换 IP 地址
            if api.datatypes.ipaddr(info) then
                local parts = {}
                for part in info:gmatch("[^%.]+") do
                    table.insert(parts, part)
                end
                if #parts == 4 then
                    local hidden = parts[1] .. ".***.***.***"
                    filtered_content = filtered_content:gsub(info, hidden)
                end
            elseif api.datatypes.ip6addr(info) then
                -- IPv6 地址隐藏
                local parts = {}
                for part in info:gmatch("[^:]+") do
                    table.insert(parts, part)
                end
                if #parts >= 2 then
                    local hidden = parts[1] .. ":***::***"
                    filtered_content = filtered_content:gsub(info, hidden)
                end
            else
                -- 域名隐藏
                local domain_parts = {}
                for part in info:gmatch("[^%.]+") do
                    table.insert(domain_parts, part)
                end
                if #domain_parts >= 2 then
                    local hidden = "***." .. domain_parts[#domain_parts]
                    filtered_content = filtered_content:gsub(info, hidden)
                end
            end
        end
    end
    
    return filtered_content
end

-- 过滤日志文件
local function filter_log_file(log_file)
    if not api.fs.access(log_file) then
        return
    end
    
    local content = api.fs.readfile(log_file)
    if content then
        local filtered_content = filter_log_content(content)
        if filtered_content ~= content then
            api.fs.writefile(log_file, filtered_content)
        end
    end
end

-- 主要的日志文件列表
local log_files = {
    "/tmp/log/passwall.log",
    "/tmp/log/passwall_server.log"
}

-- 动态日志文件（ACL 相关）
local function get_dynamic_log_files()
    local files = {}
    local acl_path = "/tmp/etc/passwall/acl"
    
    if api.fs.access(acl_path) then
        for file in api.fs.dir(acl_path) do
            if file ~= "." and file ~= ".." then
                local tcp_log = acl_path .. "/" .. file .. "/TCP.log"
                local udp_log = acl_path .. "/" .. file .. "/UDP.log"
                local chinadns_log = acl_path .. "/" .. file .. "/chinadns_ng.log"
                
                if api.fs.access(tcp_log) then
                    table.insert(files, tcp_log)
                end
                if api.fs.access(udp_log) then
                    table.insert(files, udp_log)
                end
                if api.fs.access(chinadns_log) then
                    table.insert(files, chinadns_log)
                end
            end
        end
    end
    
    return files
end

-- SOCKS 日志文件
local function get_socks_log_files()
    local files = {}
    local socks_path = "/tmp/etc/passwall"
    
    if api.fs.access(socks_path) then
        for file in api.fs.dir(socks_path) do
            if file:match("^SOCKS_.*%.log$") then
                table.insert(files, socks_path .. "/" .. file)
            end
        end
    end
    
    return files
end

-- 主函数
local function main()
    -- 过滤主要日志文件
    for _, log_file in ipairs(log_files) do
        filter_log_file(log_file)
    end
    
    -- 过滤动态日志文件
    local dynamic_files = get_dynamic_log_files()
    for _, log_file in ipairs(dynamic_files) do
        filter_log_file(log_file)
    end
    
    -- 过滤 SOCKS 日志文件
    local socks_files = get_socks_log_files()
    for _, log_file in ipairs(socks_files) do
        filter_log_file(log_file)
    end
end

-- 如果作为脚本运行
if arg and arg[0] and arg[0]:match("log_filter%.lua$") then
    main()
end

-- 导出函数供其他模块使用
return {
    filter_log_content = filter_log_content,
    filter_log_file = filter_log_file,
    main = main
}
