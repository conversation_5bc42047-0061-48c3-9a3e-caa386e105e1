﻿<%
local api = require "luci.xqnetwork.api"
-%>
<script type="text/javascript">
	//<![CDATA[
	var _users_status = document.getElementsByClassName('_users_status');
	for(var i = 0; i < _users_status.length; i++) {
		var id = _users_status[i].parentElement.parentElement.parentElement.id;
		id = id.substr(id.lastIndexOf("-") + 1);
		XHR.get('<%=api.url("server_user_status")%>', {
				index: i,
				id: id
			},
			function(x, result) {
				_users_status[result.index].setAttribute("style","font-weight:bold;");
				_users_status[result.index].setAttribute("color",result.status ? "green":"red");
				_users_status[result.index].innerHTML = (result.status ? '鉁? : 'X');
			}
		);
	}

	var edit_btn = document.getElementById("cbi-xqnetwork_server-user").getElementsByClassName("cbi-button cbi-button-edit");
	for (var i = 0; i < edit_btn.length; i++) {
		try {
			var onclick_str = edit_btn[i].getAttribute("onclick");
			var id = onclick_str.substring(onclick_str.lastIndexOf('/') + 1, onclick_str.length - 1);
			var td = edit_btn[i].parentNode;
			var new_div = "";
			//娣诲姞"鏃ュ織"鎸夐挳
			new_div += '<input class="btn cbi-button cbi-button-add" type="button" value="<%:Log%>" onclick="window.open(\'' + '<%=api.url("server_user_log")%>' + '?id=' + id + '\', \'_blank\')"/>&nbsp;&nbsp;';
			td.innerHTML = new_div + td.innerHTML;
		}
		catch(err) {
			console.error(err);
		}
	}
	//]]>
</script>

