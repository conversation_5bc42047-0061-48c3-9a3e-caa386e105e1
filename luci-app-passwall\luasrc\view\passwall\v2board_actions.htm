<%
local api = require "luci.passwall.api"
-%>
<div id="v2board_actions" style="margin: 10px 0;">
	<button type="button" class="btn cbi-button cbi-button-apply" onclick="doV2BoardLogin()">登录</button>
	<button type="button" class="btn cbi-button cbi-button-reset" onclick="doV2BoardLogout()">登出</button>
	<button type="button" class="btn cbi-button cbi-button-action" onclick="doV2BoardSyncNodes()">同步节点</button>
	
	<div id="action_result" style="margin-top: 10px; padding: 10px; border-radius: 4px; display: none;">
		<span id="result_message"></span>
	</div>
</div>

<script type="text/javascript">
//<![CDATA[
function showResult(message, isSuccess) {
	var resultDiv = document.getElementById('action_result');
	var messageSpan = document.getElementById('result_message');
	
	messageSpan.innerHTML = message;
	resultDiv.style.display = 'block';
	resultDiv.style.backgroundColor = isSuccess ? '#d4edda' : '#f8d7da';
	resultDiv.style.color = isSuccess ? '#155724' : '#721c24';
	resultDiv.style.border = '1px solid ' + (isSuccess ? '#c3e6cb' : '#f5c6cb');
	
	// 3秒后自动隐藏
	setTimeout(function() {
		resultDiv.style.display = 'none';
	}, 3000);
}

function doV2BoardLogin() {
	var baseUrl = document.querySelector('input[name="cbid.passwall.@v2board[0].base_url"]').value;
	var email = document.querySelector('input[name="cbid.passwall.@v2board[0].email"]').value;
	var password = document.querySelector('input[name="cbid.passwall.@v2board[0].password"]').value;
	
	if (!baseUrl || !email || !password) {
		showResult('请填写完整的登录信息', false);
		return;
	}
	
	showResult('正在登录...', true);
	
	XHR.post('<%=api.url("v2board_login")%>', {
		base_url: baseUrl,
		email: email,
		password: password
	}, function(x, result) {
		if (result && result.code === 0) {
			showResult('登录成功', true);
			// 刷新状态和用户信息
			setTimeout(function() {
				checkV2BoardStatus();
				loadUserInfo();
			}, 1000);
		} else {
			showResult(result ? result.message : '登录失败', false);
		}
	});
}

function doV2BoardLogout() {
	showResult('正在登出...', true);
	
	XHR.get('<%=api.url("v2board_logout")%>', null, function(x, result) {
		if (result && result.code === 0) {
			showResult('登出成功', true);
			// 刷新状态和用户信息
			setTimeout(function() {
				checkV2BoardStatus();
				loadUserInfo();
			}, 1000);
		} else {
			showResult(result ? result.message : '登出失败', false);
		}
	});
}

function doV2BoardSyncNodes() {
	showResult('正在同步节点...', true);
	
	XHR.get('<%=api.url("v2board_sync_nodes")%>', null, function(x, result) {
		if (result && result.code === 0) {
			showResult('节点同步已开始，请稍后查看节点列表', true);
		} else {
			showResult(result ? result.message : '节点同步失败', false);
		}
	});
}
//]]>
</script>
