#!/bin/bash

# XQNetwork 功能测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试文件结构
test_file_structure() {
    print_info "测试文件结构..."
    
    local files=(
        "luci-app-xqnetwork/Makefile"
        "luci-app-xqnetwork/luasrc/controller/passwall.lua"
        "luci-app-xqnetwork/luasrc/model/cbi/passwall/client/v2board_login.lua"
        "luci-app-xqnetwork/luasrc/passwall/v2board_api.lua"
        "luci-app-xqnetwork/luasrc/view/passwall/v2board_status.htm"
        "luci-app-xqnetwork/root/usr/share/passwall/log_filter.lua"
        "luci-app-xqnetwork/root/usr/share/passwall/0_default_config"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "缺少以下文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    else
        print_success "文件结构检查通过"
        return 0
    fi
}

# 测试 Lua 语法
test_lua_syntax() {
    print_info "测试 Lua 语法..."
    
    local lua_files=(
        "luci-app-xqnetwork/luasrc/controller/passwall.lua"
        "luci-app-xqnetwork/luasrc/model/cbi/passwall/client/v2board_login.lua"
        "luci-app-xqnetwork/luasrc/passwall/v2board_api.lua"
        "luci-app-xqnetwork/root/usr/share/passwall/log_filter.lua"
    )
    
    local syntax_errors=()
    
    for file in "${lua_files[@]}"; do
        if [ -f "$file" ]; then
            if ! lua -l "$file" 2>/dev/null; then
                syntax_errors+=("$file")
            fi
        fi
    done
    
    if [ ${#syntax_errors[@]} -gt 0 ]; then
        print_error "以下文件存在语法错误:"
        for file in "${syntax_errors[@]}"; do
            echo "  - $file"
        done
        return 1
    else
        print_success "Lua 语法检查通过"
        return 0
    fi
}

# 测试配置文件
test_config_file() {
    print_info "测试配置文件..."
    
    local config_file="luci-app-xqnetwork/root/usr/share/passwall/0_default_config"
    
    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查是否包含 v2board 配置段
    if ! grep -q "config v2board" "$config_file"; then
        print_error "配置文件缺少 v2board 配置段"
        return 1
    fi
    
    print_success "配置文件检查通过"
    return 0
}

# 测试 Makefile
test_makefile() {
    print_info "测试 Makefile..."
    
    local makefile="luci-app-xqnetwork/Makefile"
    
    if [ ! -f "$makefile" ]; then
        print_error "Makefile 不存在"
        return 1
    fi
    
    # 检查包名
    if ! grep -q "PKG_NAME:=luci-app-xqnetwork" "$makefile"; then
        print_error "Makefile 中包名不正确"
        return 1
    fi
    
    # 检查标题
    if ! grep -q "LUCI_TITLE:=LuCI support for XQNetwork" "$makefile"; then
        print_error "Makefile 中标题不正确"
        return 1
    fi
    
    print_success "Makefile 检查通过"
    return 0
}

# 测试脚本权限
test_script_permissions() {
    print_info "测试脚本权限..."
    
    local scripts=(
        "install.sh"
        "uninstall.sh"
        "test.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ] && [ ! -x "$script" ]; then
            print_warning "$script 没有执行权限"
            chmod +x "$script" 2>/dev/null || print_error "无法设置 $script 执行权限"
        fi
    done
    
    print_success "脚本权限检查完成"
    return 0
}

# 测试模板文件
test_template_files() {
    print_info "测试模板文件..."
    
    local templates=(
        "luci-app-xqnetwork/luasrc/view/passwall/v2board_status.htm"
        "luci-app-xqnetwork/luasrc/view/passwall/v2board_user_info.htm"
        "luci-app-xqnetwork/luasrc/view/passwall/v2board_actions.htm"
    )
    
    for template in "${templates[@]}"; do
        if [ ! -f "$template" ]; then
            print_error "模板文件不存在: $template"
            return 1
        fi
        
        # 检查是否包含必要的 JavaScript
        if ! grep -q "XHR.get\|XHR.post" "$template"; then
            print_warning "$template 可能缺少 AJAX 调用"
        fi
    done
    
    print_success "模板文件检查通过"
    return 0
}

# 测试文档完整性
test_documentation() {
    print_info "测试文档完整性..."
    
    local docs=(
        "README.md"
        "USAGE.md"
    )
    
    for doc in "${docs[@]}"; do
        if [ ! -f "$doc" ]; then
            print_error "文档文件不存在: $doc"
            return 1
        fi
        
        # 检查文档是否为空
        if [ ! -s "$doc" ]; then
            print_error "文档文件为空: $doc"
            return 1
        fi
    done
    
    print_success "文档完整性检查通过"
    return 0
}

# 生成测试报告
generate_report() {
    local total_tests=7
    local passed_tests=0
    
    echo
    echo "========================================"
    echo "           测试报告"
    echo "========================================"
    
    # 运行所有测试
    test_file_structure && ((passed_tests++))
    test_lua_syntax && ((passed_tests++))
    test_config_file && ((passed_tests++))
    test_makefile && ((passed_tests++))
    test_script_permissions && ((passed_tests++))
    test_template_files && ((passed_tests++))
    test_documentation && ((passed_tests++))
    
    echo
    echo "测试结果: $passed_tests/$total_tests 通过"
    
    if [ $passed_tests -eq $total_tests ]; then
        print_success "所有测试通过！项目可以发布。"
        return 0
    else
        print_error "有 $((total_tests - passed_tests)) 个测试失败，请修复后重试。"
        return 1
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "       XQNetwork 功能测试"
    echo "========================================"
    echo
    
    generate_report
}

# 运行主函数
main "$@"
