﻿#!/bin/bash

# XQNetwork 鍔熻兘娴嬭瘯鑴氭湰

set -e

# 棰滆壊瀹氫箟
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 鎵撳嵃甯﹂鑹茬殑娑堟伅
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 娴嬭瘯鏂囦欢缁撴瀯
test_file_structure() {
    print_info "娴嬭瘯鏂囦欢缁撴瀯..."
    
    local files=(
        "luci-app-xqnetwork/Makefile"
        "luci-app-xqnetwork/luasrc/controller/xqnetwork.lua"
        "luci-app-xqnetwork/luasrc/model/cbi/xqnetwork/client/v2board_login.lua"
        "luci-app-xqnetwork/luasrc/xqnetwork/v2board_api.lua"
        "luci-app-xqnetwork/luasrc/view/xqnetwork/v2board_status.htm"
        "luci-app-xqnetwork/root/usr/share/xqnetwork/log_filter.lua"
        "luci-app-xqnetwork/root/usr/share/xqnetwork/0_default_config"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "缂哄皯浠ヤ笅鏂囦欢:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    else
        print_success "鏂囦欢缁撴瀯妫€鏌ラ€氳繃"
        return 0
    fi
}

# 娴嬭瘯 Lua 璇硶
test_lua_syntax() {
    print_info "娴嬭瘯 Lua 璇硶..."
    
    local lua_files=(
        "luci-app-xqnetwork/luasrc/controller/xqnetwork.lua"
        "luci-app-xqnetwork/luasrc/model/cbi/xqnetwork/client/v2board_login.lua"
        "luci-app-xqnetwork/luasrc/xqnetwork/v2board_api.lua"
        "luci-app-xqnetwork/root/usr/share/xqnetwork/log_filter.lua"
    )
    
    local syntax_errors=()
    
    for file in "${lua_files[@]}"; do
        if [ -f "$file" ]; then
            if ! lua -l "$file" 2>/dev/null; then
                syntax_errors+=("$file")
            fi
        fi
    done
    
    if [ ${#syntax_errors[@]} -gt 0 ]; then
        print_error "浠ヤ笅鏂囦欢瀛樺湪璇硶閿欒:"
        for file in "${syntax_errors[@]}"; do
            echo "  - $file"
        done
        return 1
    else
        print_success "Lua 璇硶妫€鏌ラ€氳繃"
        return 0
    fi
}

# 娴嬭瘯閰嶇疆鏂囦欢
test_config_file() {
    print_info "娴嬭瘯閰嶇疆鏂囦欢..."
    
    local config_file="luci-app-xqnetwork/root/usr/share/xqnetwork/0_default_config"
    
    if [ ! -f "$config_file" ]; then
        print_error "閰嶇疆鏂囦欢涓嶅瓨鍦? $config_file"
        return 1
    fi
    
    # 妫€鏌ユ槸鍚﹀寘鍚?v2board 閰嶇疆娈?    if ! grep -q "config v2board" "$config_file"; then
        print_error "閰嶇疆鏂囦欢缂哄皯 v2board 閰嶇疆娈?
        return 1
    fi
    
    print_success "閰嶇疆鏂囦欢妫€鏌ラ€氳繃"
    return 0
}

# 娴嬭瘯 Makefile
test_makefile() {
    print_info "娴嬭瘯 Makefile..."
    
    local makefile="luci-app-xqnetwork/Makefile"
    
    if [ ! -f "$makefile" ]; then
        print_error "Makefile 涓嶅瓨鍦?
        return 1
    fi
    
    # 妫€鏌ュ寘鍚?    if ! grep -q "PKG_NAME:=luci-app-xqnetwork" "$makefile"; then
        print_error "Makefile 涓寘鍚嶄笉姝ｇ‘"
        return 1
    fi
    
    # 妫€鏌ユ爣棰?    if ! grep -q "LUCI_TITLE:=LuCI support for XQNetwork" "$makefile"; then
        print_error "Makefile 涓爣棰樹笉姝ｇ‘"
        return 1
    fi
    
    print_success "Makefile 妫€鏌ラ€氳繃"
    return 0
}

# 娴嬭瘯鑴氭湰鏉冮檺
test_script_permissions() {
    print_info "娴嬭瘯鑴氭湰鏉冮檺..."
    
    local scripts=(
        "install.sh"
        "uninstall.sh"
        "test.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ] && [ ! -x "$script" ]; then
            print_warning "$script 娌℃湁鎵ц鏉冮檺"
            chmod +x "$script" 2>/dev/null || print_error "鏃犳硶璁剧疆 $script 鎵ц鏉冮檺"
        fi
    done
    
    print_success "鑴氭湰鏉冮檺妫€鏌ュ畬鎴?
    return 0
}

# 娴嬭瘯妯℃澘鏂囦欢
test_template_files() {
    print_info "娴嬭瘯妯℃澘鏂囦欢..."
    
    local templates=(
        "luci-app-xqnetwork/luasrc/view/xqnetwork/v2board_status.htm"
        "luci-app-xqnetwork/luasrc/view/xqnetwork/v2board_user_info.htm"
        "luci-app-xqnetwork/luasrc/view/xqnetwork/v2board_actions.htm"
    )
    
    for template in "${templates[@]}"; do
        if [ ! -f "$template" ]; then
            print_error "妯℃澘鏂囦欢涓嶅瓨鍦? $template"
            return 1
        fi
        
        # 妫€鏌ユ槸鍚﹀寘鍚繀瑕佺殑 JavaScript
        if ! grep -q "XHR.get\|XHR.post" "$template"; then
            print_warning "$template 鍙兘缂哄皯 AJAX 璋冪敤"
        fi
    done
    
    print_success "妯℃澘鏂囦欢妫€鏌ラ€氳繃"
    return 0
}

# 娴嬭瘯鏂囨。瀹屾暣鎬?test_documentation() {
    print_info "娴嬭瘯鏂囨。瀹屾暣鎬?.."
    
    local docs=(
        "README.md"
        "USAGE.md"
    )
    
    for doc in "${docs[@]}"; do
        if [ ! -f "$doc" ]; then
            print_error "鏂囨。鏂囦欢涓嶅瓨鍦? $doc"
            return 1
        fi
        
        # 妫€鏌ユ枃妗ｆ槸鍚︿负绌?        if [ ! -s "$doc" ]; then
            print_error "鏂囨。鏂囦欢涓虹┖: $doc"
            return 1
        fi
    done
    
    print_success "鏂囨。瀹屾暣鎬ф鏌ラ€氳繃"
    return 0
}

# 鐢熸垚娴嬭瘯鎶ュ憡
generate_report() {
    local total_tests=7
    local passed_tests=0
    
    echo
    echo "========================================"
    echo "           娴嬭瘯鎶ュ憡"
    echo "========================================"
    
    # 杩愯鎵€鏈夋祴璇?    test_file_structure && ((passed_tests++))
    test_lua_syntax && ((passed_tests++))
    test_config_file && ((passed_tests++))
    test_makefile && ((passed_tests++))
    test_script_permissions && ((passed_tests++))
    test_template_files && ((passed_tests++))
    test_documentation && ((passed_tests++))
    
    echo
    echo "娴嬭瘯缁撴灉: $passed_tests/$total_tests 閫氳繃"
    
    if [ $passed_tests -eq $total_tests ]; then
        print_success "鎵€鏈夋祴璇曢€氳繃锛侀」鐩彲浠ュ彂甯冦€?
        return 0
    else
        print_error "鏈?$((total_tests - passed_tests)) 涓祴璇曞け璐ワ紝璇蜂慨澶嶅悗閲嶈瘯銆?
        return 1
    fi
}

# 涓诲嚱鏁?main() {
    echo "========================================"
    echo "       XQNetwork 鍔熻兘娴嬭瘯"
    echo "========================================"
    echo
    
    generate_report
}

# 杩愯涓诲嚱鏁?main "$@"

