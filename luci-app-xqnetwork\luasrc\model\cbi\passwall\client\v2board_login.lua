local api = require "luci.passwall.api"
local appname = "passwall"

m = Map(appname, translate("V2Board Login"))
api.set_apply_on_parse(m)

-- 创建 v2board 配置段（如果不存在）
if not m:get("@v2board[0]") then
	m:section("v2board", "v2board")
end

s = m:section(NamedSection, "@v2board[0]", "v2board", translate("V2Board Configuration"))
s.anonymous = true
s.addremove = false

-- V2Board 服务器地址
o = s:option(Value, "base_url", translate("V2Board Server URL"))
o.placeholder = "https://your-v2board-domain.com"
o.description = translate("Enter your V2Board server URL (with https://)")
o.rmempty = false

-- 邮箱
o = s:option(Value, "email", translate("Email"))
o.placeholder = "<EMAIL>"
o.description = translate("Your V2Board account email")
o.rmempty = false

-- 密码
o = s:option(Value, "password", translate("Password"))
o.password = true
o.description = translate("Your V2Board account password")
o.rmempty = false

-- 登录状态显示
o = s:option(DummyValue, "login_status", translate("Login Status"))
o.rawhtml = true
o.template = "passwall/v2board_status"

-- 用户信息显示
o = s:option(DummyValue, "user_info", translate("User Information"))
o.rawhtml = true
o.template = "passwall/v2board_user_info"

-- 操作按钮
o = s:option(DummyValue, "actions", translate("Actions"))
o.rawhtml = true
o.template = "passwall/v2board_actions"

return m
