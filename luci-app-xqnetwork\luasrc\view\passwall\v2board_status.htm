<%
local api = require "luci.passwall.api"
-%>
<div id="v2board_status" style="margin: 10px 0;">
	<span id="status_text" style="font-weight: bold;">检查登录状态中...</span>
	<span id="status_icon" style="margin-left: 10px;"></span>
</div>

<script type="text/javascript">
//<![CDATA[
function checkV2BoardStatus() {
	XHR.get('<%=api.url("v2board_status")%>', null, function(x, result) {
		var statusText = document.getElementById('status_text');
		var statusIcon = document.getElementById('status_icon');
		
		if (result && result.logged_in) {
			statusText.innerHTML = '已登录';
			statusText.style.color = 'green';
			statusIcon.innerHTML = '✓';
			statusIcon.style.color = 'green';
		} else {
			statusText.innerHTML = '未登录';
			statusText.style.color = 'red';
			statusIcon.innerHTML = '✗';
			statusIcon.style.color = 'red';
		}
		
		if (result && result.message) {
			statusText.title = result.message;
		}
	});
}

// 页面加载时检查状态
document.addEventListener('DOMContentLoaded', function() {
	checkV2BoardStatus();
});

// 每30秒检查一次状态
setInterval(checkV2BoardStatus, 30000);
//]]>
</script>
