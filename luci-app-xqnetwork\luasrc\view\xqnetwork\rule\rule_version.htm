﻿<%
local api = require "luci.xqnetwork.api"

local gfwlist_update = api.uci_get_type("@global_rules[0]", "gfwlist_update", "1") == "1" and "checked='checked'" or ""
local chnroute_update = api.uci_get_type("@global_rules[0]", "chnroute_update", "1") == "1" and "checked='checked'" or ""
local chnroute6_update = api.uci_get_type("@global_rules[0]", "chnroute6_update", "1") == "1" and "checked='checked'" or ""
local chnlist_update = api.uci_get_type("@global_rules[0]", "chnlist_update", "1") == "1" and "checked='checked'" or ""
local geoip_update = api.uci_get_type("@global_rules[0]", "geoip_update", "1") == "1" and "checked='checked'" or ""
local geosite_update = api.uci_get_type("@global_rules[0]", "geosite_update", "1") == "1" and "checked='checked'" or ""
-%>

<script type="text/javascript">
	//<![CDATA[
	function update_rules(btn) {
		btn.disabled = true;
		btn.value = '<%:Updating...%>';
		var div = document.getElementById('_rule_div');
		var domList = div.getElementsByTagName('input');
		var checkBoxList = [];
		var len = domList.length;
		while(len--) {
			var dom = domList[len];銆€銆€
			if(dom.type == 'checkbox' && dom.checked) {銆€銆€
				checkBoxList.push(dom.name);銆€銆€
			}
		}
		XHR.get('<%=api.url("update_rules")%>', {
				update: checkBoxList.join(",")
			},
			function(x, data) {
				if(x && x.status == 200) {
					window.location.href = '<%=api.url("log")%>';
				} else {
					alert("<%:Error%>");
					btn.disabled = false;
					btn.value = '<%:Manually update%>';
				}
			}
		);
	}
	//]]>
</script>
<div class="cbi-value" id="_rule_div">
	<label class="cbi-value-title">
		<%:Manually update%>
	</label>
	<div class="cbi-value-field">
		<div>
			<label>
				<input class="cbi-input-checkbox" type="checkbox" name="gfwlist" value="1" <%=gfwlist_update%> />
				gfwlist
			</label>
			<label>
				<input class="cbi-input-checkbox" type="checkbox" name="chnroute" value="1" <%=chnroute_update%> />
				chnroute
			</label>
			<label>
				<input class="cbi-input-checkbox" type="checkbox" name="chnroute6" value="1" <%=chnroute6_update%> />
				chnroute6
			</label>
			<label>
				<input class="cbi-input-checkbox" type="checkbox" name="chnlist" value="1" <%=chnlist_update%> />
				chnlist
			</label>
			<label>
				<input class="cbi-input-checkbox" type="checkbox" name="geoip" value="1" <%=geoip_update%> />
				geoip
			</label>
			<label>
				<input class="cbi-input-checkbox" type="checkbox" name="geosite" value="1" <%=geosite_update%> />
				geosite
			</label>
			<br><br><input class="btn cbi-button cbi-button-apply" type="button" id="update_rules_btn" onclick="update_rules(this)" value="<%:Manually update%>" />
		</div>
	</div>
</div>

