msgid "Pass Wall"
msgstr "PassWall"

msgid "Auto"
msgstr "自动"

msgid "RUNNING"
msgstr "运行中"

msgid "NOT RUNNING"
msgstr "未运行"

msgid "Working..."
msgstr "连接正常"

msgid "Problem detected!"
msgstr "连接失败"

msgid "Touch Check"
msgstr "点我检测"

msgid "Kernel Unsupported"
msgstr "内核不支持"

msgid "Settings"
msgstr "设置"

msgid "Main Settings"
msgstr "节点选择"

msgid "Basic Settings"
msgstr "基本设置"

msgid "Node List"
msgstr "节点列表"

msgid "Other Settings"
msgstr "高级设置"

msgid "Load Balancing"
msgstr "负载均衡"

msgid "Enter interface"
msgstr "进入界面"

msgid "Rule Manage"
msgstr "规则管理"

msgid "Rule List"
msgstr "规则列表"

msgid "Access control"
msgstr "访问控制"

msgid "Watch Logs"
msgstr "查看日志"

msgid "Node Config"
msgstr "节点配置"

msgid "Running Status"
msgstr "运行状态"

msgid "Baidu Connection"
msgstr "百度连接"

msgid "Google Connection"
msgstr "谷歌连接"

msgid "GitHub Connection"
msgstr "GitHub 连接"

msgid "Instagram Connection"
msgstr "Instagram 连接"

msgid "Node Check"
msgstr "节点检测"

msgid "Check..."
msgstr "检测中..."

msgid "Clear"
msgstr "清除"

msgid "Main switch"
msgstr "主开关"

msgid "TCP Node"
msgstr "TCP 节点"

msgid "UDP Node"
msgstr "UDP 节点"

msgid "Edit Current Node"
msgstr "编辑当前节点"

msgid "Socks Config"
msgstr "Socks 配置"

msgid "Socks Node"
msgstr "Socks 节点"

msgid "Listen Port"
msgstr "监听端口"

msgid "0 is not use"
msgstr "0 为不使用"

msgid "Same as the tcp node"
msgstr "与 TCP 节点相同"

msgid "Current node: %s"
msgstr "当前节点：%s"

msgid "DNS Shunt"
msgstr "DNS分流"

msgid "Domestic group name"
msgstr "国内分组名"

msgid "You only need to configure domestic DNS packets in SmartDNS and set it redirect or as Dnsmasq upstream, and fill in the domestic DNS group name here."
msgstr "您只需要在SmartDNS配置好国内DNS分组，并设置重定向或作为Dnsmasq上游，此处填入国内DNS分组名。"

msgid "Filter Mode"
msgstr "过滤模式"

msgid "If the node uses Xray/Sing-Box shunt, select the matching filter mode (Xray/Sing-Box)."
msgstr "当节点使用 Xray/Sing-Box 分流时，过滤模式需对应选择 Xray/Sing-Box 。"

msgid "A/AAAA type"
msgstr "A/AAAA 类型"

msgid "TCP node must be '%s' type to use FakeDNS."
msgstr "TCP 节点必须是 '%s' 类型才能使用 FakeDNS。"

msgid "Direct DNS"
msgstr "直连 DNS"

msgid "Direct DNS DoT"
msgstr "直连 DNS DoT"

msgid "Remote DNS"
msgstr "远程 DNS"

msgid "Resolver For The List Proxied"
msgstr "解析被代理的域名列表"

msgid "Requery DNS By %s"
msgstr "通过 %s 请求 DNS"

msgid "Socks Server"
msgstr "Socks 服务器"

msgid "Misconfigured"
msgstr "配置不当"

msgid "Make sure socks service is available on this address."
msgstr "请确保此 Socks 服务可用。"

msgid "%s request address"
msgstr "%s 请求地址"

msgid "Format must be:"
msgstr "格式必须为："

msgid "Request protocol"
msgstr "请求协议"

msgid "Remote DNS DoH"
msgstr "远程 DNS DoH"

msgid "Remote DNS DoT"
msgstr "远程 DNS DoT"

msgid "Notify the DNS server when the DNS query is notified, the location of the client (cannot be a private IP address)."
msgstr "用于 DNS 查询时通知 DNS 服务器，客户端所在的地理位置（不能是私有 IP 地址）。"

msgid "This feature requires the DNS server to support the Edns Client Subnet (RFC7871)."
msgstr "此功能需要 DNS 服务器支持 EDNS Client Subnet（RFC7871）。"

msgid "The effect is better, recommend."
msgstr "效果更好，推荐使用。"

msgid "ChinaDNS-NG (recommended)"
msgstr "ChinaDNS-NG (推荐)"

msgid "Default DNS"
msgstr "默认 DNS"

msgid "When not matching any domain name list:"
msgstr "当不匹配任何域名列表时："

msgid "Remote DNS: Can avoid more DNS leaks, but some domestic domain names maybe to proxy!"
msgstr "远程 DNS：可以避免更多的 DNS 泄露，但会导致规则列表外的某些国内域名可能会走代理！"

msgid "Direct DNS: Internet experience may be better, but DNS will be leaked!"
msgstr "直连 DNS：上网体验可能会更佳，但是会泄露 DNS！"

msgid "Smart, Do not accept no-ip reply from Direct DNS"
msgstr "智能，不接受直连 DNS 空响应"

msgid "Smart, Accept no-ip reply from Direct DNS"
msgstr "智能，接受直连 DNS 空响应"

msgid "Smart: Forward to both direct and remote DNS, if the direct DNS resolution result is a mainland China IP, then use the direct result, otherwise use the remote result."
msgstr "智能：同时转发给直连和远程 DNS，如果直连 DNS 解析结果是大陆 IP，则使用直连结果，否则使用远程结果。"

msgid "In smart mode, no-ip reply from Direct DNS:"
msgstr "使用智能模式，直连 DNS 返回空响应时:"

msgid "Do not accept: Wait and use Remote DNS Reply."
msgstr "不接受：等待并使用远程 DNS 的响应。"

msgid "Accept: Trust the Reply, using this option can improve DNS resolution speeds for some mainland IPv4-only sites."
msgstr "接受：信任空响应，使用此选项可以提升部分大陆仅 IPv4 站点的 DNS 解析速度。"

msgid "Filter Proxy Host IPv6"
msgstr "过滤代理域名 IPv6"

msgid "Experimental feature."
msgstr "实验性功能。"

msgid "Use FakeDNS work in the shunt domain that proxy."
msgstr "需要代理的分流规则域名使用 FakeDNS。"

msgid "Redirect"
msgstr "重定向"

msgid "DNS Redirect"
msgstr "DNS 重定向"

msgid "Force special DNS server to need proxy devices."
msgstr "强制需要代理的设备使用专用 DNS 服务器。"

msgid "Clear IPSET"
msgstr "清空 IPSET"

msgid "Clear NFTSET"
msgstr "清空 NFTSET"

msgid "DoT Cert verify"
msgstr "DoT 证书验证"

msgid "Verify DoT SSL cert. (May fail on some platforms!)"
msgstr "验证 DoT SSL 证书。（在某些平台可能无法验证，谨慎开启！）"

msgid "Try this feature if the rule modification does not take effect."
msgstr "如果修改规则后没有生效，请尝试此功能。"

msgid "Force HTTPS SOA"
msgstr "停用 HTTPS 记录解析"

msgid "Force queries with qtype 65 to respond with an SOA record."
msgstr "强制使 qtype 65 查询返回 SOA。"

msgid "Are you sure to hide?"
msgstr "你确定要隐藏吗？"

msgid "DNS related issues:"
msgstr "DNS 相关问题："

msgid "Certain browsers such as Chrome have built-in DNS service, which may affect DNS resolution settings. You can go to 'Settings -> Privacy and security -> Use secure DNS' menu to turn it off."
msgstr "某些浏览器如 Chrome 等内置此功能，这会影响设置的 DNS 解析。您可以进入 '设置->私隐和安全->使用安全 DNS' 菜单,将其关闭。"

msgid "If you are unable to access the internet after reboot, please try clearing the cache of your terminal devices (make sure to close all open browser application windows first, this step is especially important):"
msgstr "如果在重启后无法上网，请尝试清除终端设备的缓存（先关闭所有正在使用的浏览器应用程序，这一步尤其重要）："

msgid "For Windows systems, open Command Prompt and run the command 'ipconfig /flushdns'."
msgstr "对于Windows系统，请在命令提示符中运行命令 'ipconfig /flushdns'."

msgid "For Mac systems, open Terminal and run the command 'sudo killall -HUP mDNSResponder'."
msgstr "对于Mac系统，在终端中运行命令 'sudo killall -HUP mDNSResponder'."

msgid "For mobile devices, you can clear it by reconnecting to the network, such as toggling Airplane Mode and reconnecting to WiFi."
msgstr "对于移动设备，可通过重新接入网络的方式清除。比如开关一次飞行模式，重新连接 WiFi。"

msgid "Please make sure your device's network settings point both the DNS server and default gateway to this router, to ensure DNS queries are properly routed."
msgstr "请确认您设备的网络设置，客户端 DNS 服务器和默认网关应均指向本路由器，以确保 DNS 查询正确路由。"

msgid "Browser access:"
msgstr "浏览器访问："

msgid "Hide in main menu:"
msgstr "在主菜单中隐藏："

msgid "Show in main menu:"
msgstr "在主菜单中显示："

msgid "DNS Export Of Multi WAN"
msgstr "国内 DNS 指定解析出口"

msgid "Node Export Of Multi WAN"
msgstr "节点指定出口"

msgid "Only support Multi Wan."
msgstr "只有多线接入才有效。"

msgid "Not Specify"
msgstr "不指定"

msgid "custom"
msgstr "自定义"

msgid "Process"
msgstr "进程"

msgid "1 Process"
msgstr "单进程"

msgid "Use %s"
msgstr "使用 %s"

msgid "Close(Not use)"
msgstr "关闭（不使用）"

msgid "Proxy Mode"
msgstr "代理模式"

msgid "Default Proxy Mode"
msgstr "默认代理模式"

msgid "No Proxy"
msgstr "不代理"

msgid "Proxy"
msgstr "代理"

msgid "Global Proxy"
msgstr "全局代理"

msgid "GFW List"
msgstr "GFW 列表"

msgid "Not China List"
msgstr "中国列表以外"

msgid "China List"
msgstr "中国列表"

msgid "Localhost"
msgstr "本机"

msgid "Switch Mode"
msgstr "切换模式"

msgid "Localhost Proxy"
msgstr "路由器本机代理"

msgid "When selected, localhost can transparent proxy."
msgstr "当勾选时，路由器本机可以透明代理。"

msgid "Client Proxy"
msgstr "客户端代理"

msgid "When selected, devices in LAN can transparent proxy. Otherwise, it will not be proxy. But you can still use access control to allow the designated device to proxy."
msgstr "当勾选时，局域网内的设备可以透明代理。否则，将不代理。但您仍然可以使用访问控制允许指定的设备代理。"

msgid "Want different devices to use different proxy modes/ports/nodes? Please use access control."
msgstr "希望不同设备使用不同的代理模式/端口/节点？请使用访问控制。"

msgid "Operation"
msgstr "操作"

msgid "Add Node"
msgstr "添加节点"

msgid "Add the node via the link"
msgstr "通过链接添加节点"

msgid "Enter share links, one per line. Subscription links are not supported!"
msgstr "输入分享链接，支持多个节点，每行一个。请勿输入订阅链接！"

msgid "Please enter the correct link."
msgstr "请输入正确的链接。"

msgid "Clear all nodes"
msgstr "清空所有节点"

msgid "Are you sure to clear all nodes?"
msgstr "你确定要清空所有节点吗？"

msgid "Error"
msgstr "错误"

msgid "Delete select nodes"
msgstr "删除选择的节点"

msgid "To Top"
msgstr "置顶"

msgid "Select"
msgstr "选择"

msgid "DeSelect"
msgstr "反选"

msgid "Select all"
msgstr "全选"

msgid "DeSelect all"
msgstr "全不选"

msgid "Are you sure to delete select nodes?"
msgstr "你确定要删除选择的节点吗？"

msgid "You no select nodes !"
msgstr "你没有选择任何节点！"

msgid "Are you sure set to"
msgstr "你确定要设为"

msgid "the server?"
msgstr "服务器吗？"

msgid "You choose node is:"
msgstr "你选择的节点是："

msgid "Timeout"
msgstr "超时"

msgid "Node Remarks"
msgstr "节点备注"

msgid "Add Mode"
msgstr "添加方式"

msgid "Type"
msgstr "类型"

msgid "_balancing"
msgstr "负载均衡"

msgid "_shunt"
msgstr "分流"

msgid "_iface"
msgstr "接口"

msgid "Balancing"
msgstr "负载均衡"

msgid "Balancing Strategy"
msgstr "负载均衡策略"

msgid "Fallback Node"
msgstr "后备节点"

msgid "Use Custome Probe URL"
msgstr "使用自定义探测网址"

msgid "By default the built-in probe URL will be used, enable this option to use a custom probe URL."
msgstr "默认使用内置的探测网址，启用此选项以使用自定义探测网址。"

msgid "Probe URL"
msgstr "探测网址"

msgid "The URL used to detect the connection status."
msgstr "用于检测连接状态的网址。"

msgid "Probe Interval"
msgstr "探测间隔"

msgid "The interval between initiating probes."
msgstr "发起探测的间隔。"

msgid "The time format is numbers + units, such as '10s', '2h45m', and the supported time units are <code>s</code>, <code>m</code>, <code>h</code>, which correspond to seconds, minutes, and hours, respectively."
msgstr "时间格式为数字+单位，比如<code>&quot;10s&quot;</code>, <code>&quot;2h45m&quot;</code>，支持的时间单位有 <code>s</code>，<code>m</code>，<code>h</code>，分别对应秒、分、时。"

msgid "When the unit is not filled in, it defaults to seconds."
msgstr "未填写单位时，默认为秒。"

msgid "Preferred Node Count"
msgstr "优选节点数量"

msgid "The load balancer selects the optimal number of nodes, and traffic is randomly distributed among them."
msgstr "负载均衡器选出最优节点的个数，流量将在这几个节点中随机分配。"

msgid "Shunt"
msgstr "分流"

msgid "Preproxy"
msgstr "前置代理"

msgid "Preproxy Node"
msgstr "前置代理节点"

msgid "Set the node to be used as a pre-proxy. Each rule (including <code>Default</code>) has a separate switch that controls whether this rule uses the pre-proxy or not."
msgstr "设置用作前置代理的节点。每条规则（包括<code>默认</code>）都有独立开关控制本规则是否使用前置代理。"

msgid "Direct Connection"
msgstr "直连"

msgid "Blackhole"
msgstr "黑洞"

msgid "Default Preproxy"
msgstr "默认前置代理"

msgid "There are no available nodes, please add or subscribe nodes first."
msgstr "没有可用节点，请先添加或订阅节点。"

msgid "No shunt rules? Click me to go to add."
msgstr "没有分流规则？点我前往去添加。"

msgid "When using, localhost will connect this node first and then use this node to connect the default node."
msgstr "当使用时，本机将首先连接到此节点，然后再使用此节点连接到默认节点落地。"

msgid "Domain Strategy"
msgstr "域名解析策略"

msgid "Domain matcher"
msgstr "域名匹配算法"

msgid "'AsIs': Only use domain for routing. Default value."
msgstr "AsIs：只使用域名进行路由选择。默认值。"

msgid "'IPIfNonMatch': When no rule matches current domain, resolves it into IP addresses (A or AAAA records) and try all rules again."
msgstr "IPIfNonMatch：当域名没有匹配任何规则时，将域名解析成 IP（A 记录或 AAAA 记录）再次进行匹配。"

msgid "'IPOnDemand': As long as there is a IP-based rule, resolves the domain into IP immediately."
msgstr "IPOnDemand：当匹配时碰到任何基于 IP 的规则，将域名立即解析为 IP 进行匹配。"

msgid "Prefer IPv4"
msgstr "IPv4 优先"

msgid "Prefer IPv6"
msgstr "IPv6 优先"

msgid "IPv4 Only"
msgstr "仅 IPv4"

msgid "IPv6 Only"
msgstr "仅 IPv6"

msgid "Load balancing node list"
msgstr "负载均衡节点列表"

msgid "Load balancing node list, <a target='_blank' href='https://xtls.github.io/config/routing.html#balancerobject'>document</a>"
msgstr "负载均衡节点列表，<a target='_blank' href='https://xtls.github.io/config/routing.html#balancerobject'>文档原理</a>"

msgid "From Share URL"
msgstr "导入分享URL"

msgid "Build Share URL"
msgstr "导出分享URL"

msgid "Generate QRCode"
msgstr "生成二维码"

msgid "Export Config File"
msgstr "导出配置文件"

msgid "Import Finished"
msgstr "导入完成："

msgid "Not a supported scheme:"
msgstr "不支持这种样式的："

msgid "Invalid Share URL Format"
msgstr "无效的分享URL信息"

msgid "Paste Share URL Here"
msgstr "在此处粘贴分享信息"

msgid "Share URL to clipboard unable."
msgstr "无法分享URL到剪贴板。"

msgid "Share URL to clipboard successfully."
msgstr "成功复制分享URL到剪贴板。"

msgid "Faltal on get option, please help in debug:"
msgstr "代码错误，请协助捉虫："

msgid "Faltal on set option, please help in debug:"
msgstr "代码错误，请协助捉虫："

msgid "Address"
msgstr "地址"

msgid "Address (Support Domain Name)"
msgstr "地址（支持域名）"

msgid "Trojan Verify Cert"
msgstr "验证证书"

msgid "Trojan Cert Path"
msgstr "证书路径"

msgid "Finger Print"
msgstr "指纹伪造"

msgid "Avoid using randomized, unless you have to."
msgstr "避免使用 randomized , 除非你必须要。"

msgid "Original"
msgstr "原版"

msgid "Transport Plugin"
msgstr "传输层插件"

msgid "Shadowsocks secondary encryption"
msgstr "Shadowsocks 二次加密"

msgid "Obfs Type"
msgstr "混淆类型"

msgid "Obfs Password"
msgstr "混淆密码"

msgid "Auth Type"
msgstr "认证类型"

msgid "Auth Password"
msgstr "认证密码"

msgid "Commands the client to use the BBR flow control algorithm"
msgstr "命令客户端使用 BBR 流量控制算法"

msgid "PinSHA256"
msgstr "PinSHA256"

msgid "Certificate fingerprint"
msgstr "证书指纹"

msgid "Max upload Mbps"
msgstr "最大上行(Mbps)"

msgid "Max download Mbps"
msgstr "最大下行(Mbps)"

msgid "QUIC stream receive window"
msgstr "QUIC 流接收窗口"

msgid "QUIC connection receive window"
msgstr "QUIC 连接接收窗口"

msgid "QUIC concurrent bidirectional streams"
msgstr "QUIC 并发双向流的最大数量"

msgid "Disable MTU detection"
msgstr "禁用 MTU 检测"

msgid "ignoreClientBandwidth"
msgstr "忽略客户端带宽设置"

msgid "Lazy Start"
msgstr "延迟启动"

msgid "Encrypt Method"
msgstr "加密"

msgid "Latency"
msgstr "延迟"

msgid "Automatic detection delay"
msgstr "自动检测延迟"

msgid "Show server address and port"
msgstr "显示服务器地址和端口"

msgid "URL Test"
msgstr "URL 测试"

msgid "Test"
msgstr "测试"

msgid "Node num"
msgstr "节点数量"

msgid "Self add"
msgstr "自添"

msgid "Apply"
msgstr "应用"

msgid "Use"
msgstr "使用"

msgid "Copy"
msgstr "复制"

msgid "Delay Settings"
msgstr "定时配置"

msgid "Open and close Daemon"
msgstr "启动守护进程"

msgid "Delay Start"
msgstr "开机时延时启动"

msgid "Units:seconds"
msgstr "单位：秒"

msgid "Units:minutes"
msgstr "单位：分钟"

msgid "stop automatically mode"
msgstr "定时关闭模式"

msgid "stop Time(Every day)"
msgstr "关闭时间(每天)"

msgid "stop Interval(Hour)"
msgstr "关闭间隔(小时)"

msgid "start automatically mode"
msgstr "定时开启模式"

msgid "start Time(Every day)"
msgstr "开启时间(每天)"

msgid "start Interval(Hour)"
msgstr "开启间隔(小时)"

msgid "restart automatically mode"
msgstr "定时重启模式"

msgid "restart Time(Every day)"
msgstr "重启时间(每天)"

msgid "restart Interval(Hour)"
msgstr "重启间隔(小时)"

msgid "Forwarding Settings"
msgstr "转发配置"

msgid "TCP No Redir Ports"
msgstr "TCP 不转发端口"

msgid "UDP No Redir Ports"
msgstr "UDP 不转发端口"

msgid "Fill in the ports you don't want to be forwarded by the agent, with the highest priority."
msgstr "填写你不希望被代理转发的端口，优先级最高。"

msgid "The port settings support single ports and ranges.<br>Separate multiple ports with commas (,).<br>Example: 21,80,443,1000:2000."
msgstr "以上端口设置支持单端口和端口范围。<br>多个端口用英文逗号(,)隔开。<br>例：21,80,443,1000:2000。"

msgid "TCP Proxy Drop Ports"
msgstr "TCP 转发屏蔽端口"

msgid "UDP Proxy Drop Ports"
msgstr "UDP 转发屏蔽端口"

msgid "TCP Redir Ports"
msgstr "TCP 转发端口"

msgid "UDP Redir Ports"
msgstr "UDP 转发端口"

msgid "No patterns are used"
msgstr "不使用"

msgid "All"
msgstr "所有"

msgid "Common Use"
msgstr "常用的"

msgid "Only Web"
msgstr "仅网页"

msgid "Default"
msgstr "默认"

msgid "Close"
msgstr "关闭"

msgid "Hijacking ICMP (PING)"
msgstr "劫持ICMP (PING)"

msgid "Hijacking ICMPv6 (IPv6 PING)"
msgstr "劫持ICMPv6 (IPv6 PING)"

msgid "Sniffing"
msgstr "流量嗅探"

msgid "TCP Proxy Way"
msgstr "TCP 代理方式"

msgid "Proxy Settings"
msgstr "代理配置"

msgid "Auto Switch"
msgstr "自动切换"

msgid "How often to test"
msgstr "多久检测一次"

msgid "Timeout seconds"
msgstr "超时秒数"

msgid "Timeout retry num"
msgstr "超时重试次数"

msgid "Main node"
msgstr "主节点"

msgid "List of backup nodes"
msgstr "备用节点的列表"

msgid "Restore Switch"
msgstr "恢复切换"

msgid "When detects main node is available, switch back to the main node."
msgstr "当检测到主节点可用时，切换回主节点。"

msgid "If the main node is V2ray/Xray shunt"
msgstr "如果主节点是 V2ray/Xray 分流"

msgid "Switch it"
msgstr "切掉它"

msgid "Applying to the default node"
msgstr "应用于默认节点"

msgid "Applying to the preproxy node"
msgstr "应用于前置代理节点"

msgid "Add nodes to the standby node list by keywords"
msgstr "通过关键字添加节点到备用节点列表"

msgid "Delete nodes in the standby node list by keywords"
msgstr "通过关键字删除备用节点列表的节点"

msgid "Please enter the node keyword, pay attention to distinguish between spaces, uppercase and lowercase."
msgstr "请输入节点关键字，注意区分空格、大写和小写。"

msgid "Configure this node with 127.0.0.1: this port"
msgstr "使用 127.0.0.1 和此端口配置节点"

msgid "Enable Load Balancing"
msgstr "开启负载均衡"

msgid "Console Login Auth"
msgstr "控制台登录认证"

msgid "Console Username"
msgstr "控制台账号"

msgid "Console Password"
msgstr "控制台密码"

msgid "Console Port"
msgstr "控制台端口"

msgid "In the browser input routing IP plus port access, such as:***********:1188"
msgstr "在浏览器输入路由IP加端口访问，如：***********:1188"

msgid "Haproxy Port"
msgstr "负载均衡端口"

msgid "Health Check Type"
msgstr "健康检查类型"

msgid "Inner implement"
msgstr "内置实现"

msgid "Health Check Inter"
msgstr "健康检查节点间隔时间"

msgid "When the URL test is used, the load balancing node will be converted into a Socks node. when node list set customizing, must be a Socks node, otherwise the health check will be invalid."
msgstr "当使用 URL 测试时，负载均衡节点将转换成 Socks 节点。下面的节点列表自定义时必须为 Socks 节点，否则健康检查将无效。"

msgid "Add a node, Export Of Multi WAN Only support Multi Wan. Load specific gravity range 1-256. Multiple primary servers can be load balanced, standby will only be enabled when the primary server is offline! Multiple groups can be set, Haproxy port same one for each group."
msgstr "添加节点，指定出口功能是为多 WAN 用户准备的。负载比重范围 1-256。多个主服务器可以负载均衡，备用只有在主服务器离线时才会启用！可以设置多个组，负载均衡端口相同则为一组。"

msgid "Note that the node configuration parameters for load balancing must be consistent when use TCP health check type, otherwise it cannot be used normally!"
msgstr "注意，当使用 TCP 健康检查时负载均衡的节点配置参数必须一致，否则无法正常使用！"

msgid "Node"
msgstr "节点"

msgid "Node Address"
msgstr "节点地址"

msgid "Node Port"
msgstr "节点端口"

msgid "Node Weight"
msgstr "负载比重"

msgid "Export Of Multi WAN"
msgstr "多 WAN 指定出口"

msgid "Main"
msgstr "主要"

msgid "Mode"
msgstr "模式"

msgid "Primary"
msgstr "主要"

msgid "Standby"
msgstr "备用"

msgid "Check update"
msgstr "检查更新"

msgid "Force update"
msgstr "强制更新"

msgid "Manually update"
msgstr "手动更新"

msgid "The latest version: %s, currently does not support automatic update, if you need to update, please compile or download the ipk and then manually install."
msgstr "最新版本：%s，目前暂不支持自动更新，如需更新，请自行编译或下载 ipk 然后手动安装。"

msgid "Enable custom URL"
msgstr "启用自定义规则地址"

msgid "GFW domains(gfwlist) Update URL"
msgstr "防火墙域名列表(gfwlist)更新URL"

msgid "China IPs(chnroute) Update URL"
msgstr "中国IP段(chnroute)更新URL"

msgid "China IPv6s(chnroute6) Update URL"
msgstr "中国IPv6段(chnroute6)更新URL"

msgid "China List(Chnlist) Update URL"
msgstr "中国域名列表(Chnlist)更新URL"

msgid "Rule status"
msgstr "规则版本"

msgid "Enable auto update rules"
msgstr "开启自动更新规则"

msgid "Update Time(every day)"
msgstr "更新时间(每天)"

msgid "Update Interval(hour)"
msgstr "更新间隔(小时)"

msgid "Update Mode"
msgstr "更新模式"

msgid "Loop Mode"
msgstr "循环"

msgid "Every day"
msgstr "每天"

msgid "Every Monday"
msgstr "每周一"

msgid "Every Tuesday"
msgstr "每周二"

msgid "Every Wednesday"
msgstr "每周三"

msgid "Every Thursday"
msgstr "每周四"

msgid "Every Friday"
msgstr "每周五"

msgid "Every Saturday"
msgstr "每周六"

msgid "Every Sunday"
msgstr "每周日"

msgid "hour"
msgstr "小时"

msgid "Hour"
msgstr "小时"

msgid "GeoIP Update URL"
msgstr "GeoIP 更新URL"

msgid "Geosite Update URL"
msgstr "Geosite 更新URL"

msgid "Location of Geo rule files"
msgstr "Geo 规则文件目录"

msgid "This variable specifies a directory where geoip.dat and geosite.dat files are."
msgstr "此变量指定 geoip.dat 和 geosite.dat 文件所在的目录。"

msgid "Enable Geo Data Parsing"
msgstr "开启 Geo 数据解析"

msgid "Analyzes and preloads GeoIP/Geosite data to enhance the shunt performance of Sing-box/Xray."
msgstr "分析和预加载 GeoIP/Geosite 数据，以增强 Sing-box/Xray 的分流效果。"

msgid "Once enabled, the rule list can support GeoIP/Geosite rules."
msgstr "启用后，规则列表可以支持 GeoIP/Geosite 规则。"

msgid "Note: Increases resource usage; Geosite analysis is only supported in ChinaDNS-NG and SmartDNS modes."
msgstr "注：会增加一些系统资源的开销，仅在 ChinaDNS-NG 和 SmartDNS 模式下支持分析 Geosite 。"

msgid "Shunt Rule"
msgstr "分流规则"

msgid "Please note attention to the priority, the higher the order, the higher the priority."
msgstr "请注意优先级问题，排序越上面优先级越高。"

msgid "Update..."
msgstr "更新中"

msgid "It is the latest version"
msgstr "已是最新版本"

msgid "Update successful"
msgstr "更新成功"

msgid "Click to update"
msgstr "点击更新"

msgid "Updating..."
msgstr "更新中"

msgid "Retry"
msgstr "重试"

msgid "Unexpected error"
msgstr "意外错误"

msgid "Updating, are you sure to close?"
msgstr "正在更新，你确认要关闭吗？"

msgid "Downloading..."
msgstr "下载中"

msgid "Unpacking..."
msgstr "解压中"

msgid "Moving..."
msgstr "移动中"

msgid "App Update"
msgstr "组件更新"

msgid "Please confirm that your firmware supports FPU."
msgstr "请确认你的固件支持 FPU。"

msgid "if you want to run from memory, change the path, /tmp beginning then save the application and update it manually."
msgstr "如果你希望从内存中运行，请更改路径，/tmp 开头，然后保存应用后，再手动更新。"

msgid "Make sure there is enough space to install %s"
msgstr "确保有足够的空间安装 %s"

msgid "App Path"
msgstr "程序路径"

msgid "%s App Path"
msgstr "%s 程序路径"

msgid "%s Client App Path"
msgstr "%s 客户端程序路径"

msgid "alternate API URL for version checking"
msgstr "用于版本检查的 API URL"

msgid "Node Subscribe"
msgstr "节点订阅"

msgid "Subscribe Remark"
msgstr "订阅备注（机场）"

msgid "Subscribe Info"
msgstr "订阅信息"

msgid "Subscribe URL"
msgstr "订阅网址"

msgid "Subscribe URL Access Method"
msgstr "订阅网址访问方式"

msgid "Please input the subscription url first, save and submit before manual subscription."
msgstr "请输入订阅网址保存应用后再手动订阅。"

msgid "Subscribe via proxy"
msgstr "通过代理订阅"

msgid "Enable auto update subscribe"
msgstr "开启自动更新订阅"

msgid "Manual subscription"
msgstr "手动订阅"

msgid "Delete All Subscribe Node"
msgstr "删除所有订阅节点"

msgid "Delete the subscribed node"
msgstr "删除已订阅的节点"

msgid "Are you sure you want to delete all subscribed nodes?"
msgstr "您确定要删除所有已订阅的节点吗？"

msgid "Manual subscription All"
msgstr "手动订阅全部"

msgid "This remark already exists, please change a new remark."
msgstr "此备注已存在，请改一个新的备注。"

msgid "Filter keyword Mode"
msgstr "过滤关键字模式"

msgid "Discard List"
msgstr "丢弃列表"

msgid "Keep List"
msgstr "保留列表"

msgid "Discard List,But Keep List First"
msgstr "丢弃列表，但保留列表优先"

msgid "Keep List,But Discard List First"
msgstr "保留列表，但丢弃列表优先"

msgid "Use global config"
msgstr "使用全局配置"

msgid "User-Agent"
msgstr "用户代理(User-Agent)"

msgid "Add"
msgstr "添加"

msgid "ACLs"
msgstr "访问控制"

msgid "ACLs is a tools which used to designate specific IP proxy mode."
msgstr "访问控制列表是用于指定特殊 IP 代理模式的工具。"

msgid "Example:"
msgstr "例："

msgid "IP range"
msgstr "IP 范围"

msgid "Source Interface"
msgstr "源接口"

msgid "Use Interface With ACLs"
msgstr "使用接口控制"

msgid "Remarks"
msgstr "备注"

msgid "Direct List"
msgstr "直连列表"

msgid "Proxy List"
msgstr "代理列表"

msgid "Block List"
msgstr "屏蔽列表"

msgid "Lan IP List"
msgstr "局域网 IP 列表"

msgid "Route Hosts"
msgstr "路由 Hosts 文件"

msgid "Join the direct hosts list of domain names will not proxy."
msgstr "加入的域名不走代理，对所有模式有效。且优先级最高。"

msgid "These had been joined ip addresses will not proxy. Please input the ip address or ip address segment,every line can input only one ip address. For example: ***********/24 or *********."
msgstr "加入的 IP 段不走代理，对所有模式有效。且优先级最高。可输入 IP 地址或地址段，如：***********/24 或 *********，每个地址段一行。"

msgid "These had been joined websites will use proxy. Please input the domain names of websites, every line can input only one website domain. For example: google.com."
msgstr "加入的域名将走代理。输入网站域名，如：google.com，每个地址段一行。"

msgid "These had been joined ip addresses will use proxy. Please input the ip address or ip address segment, every line can input only one ip address. For example: *********/24 or *******."
msgstr "加入的 IP 段将走代理。可输入 IP 地址或地址段，如：*********/24 或 *******，每个地址段一行。"

msgid "These had been joined websites will be block. Please input the domain names of websites, every line can input only one website domain. For example: twitter.com."
msgstr "加入的域名将屏蔽。输入网站域名，如：twitter.com，每个地址段一行。"

msgid "The list is the IPv4 LAN IP list, which represents the direct connection IP of the LAN. If you need the LAN IP in the proxy list, please clear it from the list. Do not modify this list by default."
msgstr "列表中为 IPv4 的局域网 IP 列表，代表局域网直连 IP。如果需要代理列表中的局域网 IP，请将其在该列表中清除，并将其添加到代理列表中。默认情况下不要修改这个列表。"

msgid "The list is the IPv6 LAN IP list, which represents the direct connection IP of the LAN. If you need the LAN IP in the proxy list, please clear it from the list. Do not modify this list by default."
msgstr "列表中为 IPv6 的局域网 IP 列表，代表局域网直连 IP。如果需要代理列表中的局域网 IP，请将其在该列表中清除，并将其添加到代理列表中。默认情况下不要修改这个列表。"

msgid "Configure routing etc/hosts file, if you don't know what you are doing, please don't change the content."
msgstr "配置路由 etc/hosts 文件，如果你不知道自己在做什么，请不要改动内容。"

msgid "These had been joined ip addresses will be block. Please input the ip address or ip address segment, every line can input only one ip address."
msgstr "加入的 IP 段将屏蔽。可输入 IP 地址或地址段，每个地址段一行。"

msgid "Inbound Tag"
msgstr "入站标签"

msgid "Transparent proxy"
msgstr "透明代理"

msgid "Not valid domain name, please re-enter!"
msgstr "不是有效域名，请重新输入！"

msgid "Not valid IP format, please re-enter!"
msgstr "不是有效 IP 格式，请重新输入！"

msgid "Not valid IPv4 format, please re-enter!"
msgstr "不是有效 IPv4 格式，请重新输入！"

msgid "Not valid IPv6 format, please re-enter!"
msgstr "不是有效 IPv6 格式，请重新输入！"

msgid "Not true format, please re-enter!"
msgstr "不是正确的格式，请重新输入！"

msgid "Plaintext: If this string matches any part of the targeting domain, this rule takes effet. Example: rule 'sina.com' matches targeting domain 'sina.com', 'sina.com.cn' and 'www.sina.com', but not 'sina.cn'."
msgstr "纯字符串: 当此字符串匹配目标域名中任意部分，该规则生效。比如'sina.com'可以匹配'sina.com'、'sina.com.cn'和'www.sina.com'，但不匹配'sina.cn'。"

msgid "Regular expression: Begining with 'regexp:', the rest is a regular expression. When the regexp matches targeting domain, this rule takes effect. Example: rule 'regexp:\\.goo.*\\.com$' matches 'www.google.com' and 'fonts.googleapis.com', but not 'google.com'."
msgstr "正则表达式: 由'regexp:'开始，余下部分是一个正则表达式。当此正则表达式匹配目标域名时，该规则生效。例如'regexp:\\.goo.*\\.com$'匹配'www.google.com'、'fonts.googleapis.com'，但不匹配'google.com'。"

msgid "Subdomain (recommended): Begining with 'domain:' and the rest is a domain. When the targeting domain is exactly the value, or is a subdomain of the value, this rule takes effect. Example: rule 'domain:v2ray.com' matches 'www.v2ray.com', 'v2ray.com', but not 'xv2ray.com'."
msgstr "子域名 (推荐): 由'domain:'开始，余下部分是一个域名。当此域名是目标域名或其子域名时，该规则生效。例如'domain:v2ray.com'匹配'www.v2ray.com'、'v2ray.com'，但不匹配'xv2ray.com'。"

msgid "Full domain: Begining with 'full:' and the rest is a domain. When the targeting domain is exactly the value, the rule takes effect. Example: rule 'domain:v2ray.com' matches 'v2ray.com', but not 'www.v2ray.com'."
msgstr "完整匹配: 由'full:'开始，余下部分是一个域名。当此域名完整匹配目标域名时，该规则生效。例如'full:v2ray.com'匹配'v2ray.com'但不匹配'www.v2ray.com'。"

msgid "Pre-defined domain list: Begining with 'geosite:' and the rest is a name, such as geosite:google or geosite:cn."
msgstr "预定义域名列表：由'geosite:'开头，余下部分是一个名称，如geosite:google或者geosite:cn。"

msgid "Annotation: Begining with #"
msgstr "注释: 由 # 开头"

msgid "IP: such as '127.0.0.1'."
msgstr "IP: 形如'127.0.0.1'。"

msgid "CIDR: such as '*********/8'."
msgstr "CIDR: 形如'10.0.0.0/8'."

msgid "GeoIP: such as 'geoip:cn'. It begins with geoip: (lower case) and followed by two letter of country code."
msgstr "GeoIP: 形如'geoip:cn'，必须以geoip:（小写）开头，后面跟双字符国家代码，支持几乎所有可以上网的国家。"

msgid "Clear logs"
msgstr "清空日志"

msgid "Only recommend to use with VLESS-TCP-XTLS-Vision."
msgstr "只推荐与 VLESS-TCP-XTLS-Vision 搭配使用。"

msgid "Password"
msgstr "密码"

msgid "IV Check"
msgstr "IV 检查"

msgid "UDP over TCP"
msgstr "TCP 封装 UDP"

msgid "Connection Timeout"
msgstr "连接超时时间"

msgid "Local Port"
msgstr "本地端口"

msgid "Fast Open"
msgstr "快速打开"

msgid "Need node support required"
msgstr "需要节点支持"

msgid "plugin"
msgstr "插件"

msgid "Supports custom SIP003 plugins, Make sure the plugin is installed."
msgstr "支持自定义 SIP003 插件，请确保插件已安装。"

msgid "opts"
msgstr "插件选项"

msgid "Protocol"
msgstr "协议名称"

msgid "Protocol_param"
msgstr "协议参数"

msgid "Obfs"
msgstr "混淆"

msgid "Obfs_param"
msgstr "混淆参数"

msgid "Plugin Name"
msgstr "插件名称"

msgid "Plugin Arguments"
msgstr "插件参数"

msgid "Naiveproxy Protocol"
msgstr "Naiveproxy 协议"

msgid "V2ray Protocol"
msgstr "V2ray 协议"

msgid "User Level"
msgstr "用户等级(level)"

msgid "Transport"
msgstr "传输方式"

msgid "Public Key"
msgstr "公钥"

msgid "Private Key"
msgstr "私钥"

msgid "Pre shared key"
msgstr "额外的对称加密密钥"

msgid "Local Address"
msgstr "本地地址"

msgid "Decimal numbers separated by \",\" or Base64-encoded strings."
msgstr "用“,”隔开的十进制数字或 Base64 编码字符串。"

msgid "Camouflage Domain"
msgstr "伪装域名"

msgid "Camouflage Type"
msgstr "伪装类型"

msgid "Transport Layer Encryption"
msgstr "传输层加密"

msgid "Whether or not transport layer encryption is enabled, \"none\" for unencrypted, \"tls\" for using TLS, \"xtls\" for using XTLS."
msgstr "是否启入传输层加密，支持的选项有 \"none\" 表示不加密，\"tls\" 表示使用 TLS，\"xtls\" 表示使用 XTLS。"

msgid "Original Trojan only supported 'tls', please choose 'tls'."
msgstr "原版Trojan只支持'tls'，请选择'tls'。"

msgid "Transfer mode"
msgstr "传输模式"

msgid "Do not send server name in ClientHello."
msgstr "不要在 ClientHello 中发送服务器名称。"

msgid "Domain"
msgstr "域名"

msgid "allowInsecure"
msgstr "允许不安全连接"

msgid "Whether unsafe connections are allowed. When checked, Certificate validation will be skipped."
msgstr "是否允许不安全连接。当勾选时，将跳过证书验证。"

msgid "%s Node Use Type"
msgstr "%s 节点使用类型"

msgid "Set the TUIC proxy server ip address"
msgstr "指定远程 TUIC 服务器 IP"

msgid "TUIC User Password For Connect Remote Server"
msgstr "用于远程 TUIC 服务器连接的密码"

msgid "TUIC UserName For Local Socks"
msgstr "用于本地 Socks 服务器连接的用户名"

msgid "TUIC Password For Local Socks"
msgstr "用于本地 Socks 服务器连接的密码"

msgid "UDP relay mode"
msgstr "UDP 中继模式"

msgid "Congestion control algorithm"
msgstr "拥塞控制算法"

msgid "Heartbeat interval(second)"
msgstr "保活心跳包发送间隔（单位：秒）"

msgid "Timeout for establishing a connection to server(second)"
msgstr "连接超时时间（单位：秒）"

msgid "Garbage collection interval(second)"
msgstr "UDP 数据包片残片清理间隔（单位：秒）"

msgid "Garbage collection lifetime(second)"
msgstr "UDP 数据包残片在服务器的保留时间（单位：秒）"

msgid "Disable SNI"
msgstr "关闭 SNI 服务器名称指示"

msgid "Enable 0-RTT QUIC handshake"
msgstr "客户端启用 0-RTT QUIC 连接握手"

msgid "TUIC send window"
msgstr "发送窗口（无需确认即可发送的最大字节数：默认8Mb*2）"

msgid "TUIC receive window"
msgstr "接收窗口（无需确认即可接收的最大字节数：默认8Mb）"

msgid "TUIC Maximum packet size the socks5 server can receive from external, in bytes"
msgstr "TUIC socks5 服务器可以从外部接收的最大数据包大小（以字节为单位）"

msgid "Set if the listening socket should be dual-stack"
msgstr "设置监听套接字为双栈"

msgid "<br />none: default, no masquerade, data sent is packets with no characteristics.<br />srtp: disguised as an SRTP packet, it will be recognized as video call data (such as FaceTime).<br />utp: packets disguised as uTP will be recognized as bittorrent downloaded data.<br />wechat-video: packets disguised as WeChat video calls.<br />dtls: disguised as DTLS 1.2 packet.<br />wireguard: disguised as a WireGuard packet. (not really WireGuard protocol)<br />dns: Disguising traffic as DNS requests."
msgstr "<br />none：默认值，不进行伪装，发送的数据是没有特征的数据包。<br />srtp：伪装成 SRTP 数据包，会被识别为视频通话数据（如 FaceTime）。<br />utp：伪装成 uTP 数据包，会被识别为 BT 下载数据。<br />wechat-video：伪装成微信视频通话的数据包。<br />dtls：伪装成 DTLS 1.2 数据包。<br />wireguard：伪装成 WireGuard 数据包。(并不是真正的 WireGuard 协议)<br />dns：把流量伪装成 DNS 请求。"

msgid "Use it together with the DNS disguised type. You can fill in any domain."
msgstr "配合伪装类型 DNS 使用，可随便填一个域名。"

msgid "A legal file path. This file must not exist before running."
msgstr "一个合法的文件路径。在运行之前，这个文件必须不存在。"

msgid "Auth"
msgstr "身份认证"

msgid "Socks for authentication"
msgstr "Socks 认证方式"

msgid "Socks protocol authentication, support anonymous and password."
msgstr "Socks 协议的认证方式，支持匿名方式和账号密码方式。"

msgid "anonymous"
msgstr "匿名"

msgid "User Password"
msgstr "账号密码"

msgid "Username and Password must be used together!"
msgstr "账号和密码必须同时使用！"

msgid "Node Number"
msgstr "节点数量"

msgid "You can only set up a maximum of %s nodes for the time being, Used for access control."
msgstr "目前最多只能设置 %s 个节点，用于给访问控制使用。"

msgid "Firewall tools"
msgstr "防火墙工具"

msgid "IPv6 TProxy"
msgstr "IPv6 透明代理(TProxy)"

msgid "Experimental feature. Make sure that your node supports IPv6."
msgstr "实验特性，请确保你的节点支持IPv6"

msgid "Status info"
msgstr "状态信息"

msgid "Big icon"
msgstr "大图标"

msgid "Show node check"
msgstr "显示节点检测"

msgid "Show Show IP111"
msgstr "显示 IP111"

msgid "Destination protocol"
msgstr "目标协议"

msgid "Destination address"
msgstr "目标地址"

msgid "Destination port"
msgstr "目标端口"

msgid "Whether to receive PROXY protocol, when this node want to be fallback or forwarded by proxy, it must be enable, otherwise it cannot be used."
msgstr "是否接收 PROXY protocol，当该节点要被回落或被代理转发时，必须启用，否则不能使用。"

msgid "outbound node"
msgstr "出站节点"

msgid "Custom Socks"
msgstr "自定义 Socks"

msgid "Custom HTTP"
msgstr "自定义 HTTP"

msgid "Custom Interface"
msgstr "自定义接口"

msgid "Interface"
msgstr "接口"

msgid "Bind Local"
msgstr "本机监听"

msgid "When selected, it can only be accessed localhost."
msgstr "当勾选时，只能本机访问。"

msgid "Accept LAN Access"
msgstr "接受局域网访问"

msgid "When selected, it can accessed lan , this will not be safe!"
msgstr "当勾选时，可以直接访问局域网，这将不安全！（非特殊情况不建议开启）"

msgid "Enable Remote"
msgstr "启用转发"

msgid "You can forward to Nginx/Caddy/V2ray/Xray WebSocket and more."
msgstr "您可以转发到 Nginx/Caddy/V2ray/Xray WebSocket 等。"

msgid "Remote Address"
msgstr "远程地址"

msgid "Remote Port"
msgstr "远程端口"

msgid "as:"
msgstr "如："

msgid "Public key absolute path"
msgstr "公钥文件绝对路径"

msgid "Private key absolute path"
msgstr "私钥文件绝对路径"

msgid "Can't find this file!"
msgstr "找不到这个文件！"

msgid "Public key and Private key path can not be empty!"
msgstr "公钥和私钥文件路径不能为空！"

msgid "Server-Side"
msgstr "服务器端"

msgid "Server Config"
msgstr "服务器配置"

msgid "Users Manager"
msgstr "用户管理"

msgid "Logs"
msgstr "日志"

msgid "Log"
msgstr "日志"

msgid "%s Node Log"
msgstr "%s 节点日志"

msgid "Log Level"
msgstr "日志等级"

msgid "Advanced log feature"
msgstr "高级日志功能"

msgid "For professionals only."
msgstr "仅限专业人士使用。"

msgid "Persist log file directory"
msgstr "持久性日志文件目录"

msgid "The path to the directory used to store persist log files, the \"/\" at the end can be omitted. Leave it blank to disable this feature."
msgstr "用来存储持久性日志文件的目录路径，末尾的 “/” 可以省略。留空以禁用此功能。"

msgid "Logging to system log"
msgstr "记录到系统日志"

msgid "Logging to the system log for more advanced functions. For example, send logs to a dedicated log server."
msgstr "将日志记录到系统日志，以实现更加高级的功能。例如，把日志发送到专门的日志服务器。"

msgid "Log Event Filter"
msgstr "日志事件过滤器"

msgid "Support regular expression."
msgstr "支持正则表达式。"

msgid "Shell Command"
msgstr "Shell 命令"

msgid "Shell command to execute, replace log content with %s."
msgstr "要执行的 Shell 命令，用 %s 代替日志内容。"

msgid "Not enabled log"
msgstr "未启用日志"

msgid "It is recommended to disable logging during regular use to reduce system overhead."
msgstr "正常使用时建议关闭日志，以减少系统开销。"

msgid "UDP Forward"
msgstr "UDP 转发"

msgid "DNS Settings"
msgstr "DNS 设置"

msgid "Null"
msgstr "无"

msgid "You did not fill in the %s path. Please save and apply then update manually."
msgstr "您没有填写 %s 路径。请保存应用后再手动更新。"

msgid "Not installed %s, Can't unzip!"
msgstr "未安装 %s，无法解压！"

msgid "Can't determine ARCH, or ARCH not supported."
msgstr "无法确认ARCH架构，或是不支持。"

msgid "Get remote version info failed."
msgstr "获取远程版本信息失败。"

msgid "New version found, but failed to get new version download url."
msgstr "发现新版本，但未能获得新版本的下载地址。"

msgid "Download url is required."
msgstr "请指定下载地址。"

msgid "File download failed or timed out: %s"
msgstr "文件下载失败或超时：%s"

msgid "File path required."
msgstr "请指定文件路径。"

msgid "%s not enough space."
msgstr "%s 空间不足。"

msgid "Can't find client in file: %s"
msgstr "无法在文件中找到客户端：%s"

msgid "Client file is required."
msgstr "请指定客户端文件。"

msgid "The client file is not suitable for current device."
msgstr "客户端文件不适合当前设备。"

msgid "Can't move new file to path: %s"
msgstr "无法移动新文件到：%s"

msgid "An XHttpObject in JSON format, used for sharing."
msgstr "JSON 格式的 XHttpObject，用来实现分享。"

msgid "Enable Mux.Cool"
msgstr "启用 Mux.Cool"

msgid "Mux concurrency"
msgstr "最大并发连接数"

msgid "XUDP Mux concurrency"
msgstr "XUDP 最大并发连接数"

msgid "Padding"
msgstr "填充"

msgid "Enable early data"
msgstr "启用前置数据"

msgid "Early data length"
msgstr "前置数据最大长度"

msgid "Early data header name"
msgstr "前置数据 HTTP 头名"

msgid "Recommended value: Sec-WebSocket-Protocol"
msgstr "推荐值：Sec-WebSocket-Protocol"

msgid "Health check"
msgstr "健康检查"

msgid "Health check timeout"
msgstr "检查超时时间"

msgid "Permit without stream"
msgstr "无子连接时的健康检查"

msgid "Initial Windows Size"
msgstr "初始窗口大小"

msgid "Excluded Domains"
msgstr "排除域名"

msgid "If the traffic sniffing result is in this list, the destination address will not be overridden."
msgstr "如果流量嗅探结果在此列表中，则不会覆盖目标地址。"

msgid "Buffer Size"
msgstr "缓冲区大小"

msgid "Buffer size for every connection (kB)"
msgstr "每一个连接的缓冲区大小（kB）"

msgid "Handshake Timeout"
msgstr "握手超时 "

msgid "Idle Timeout"
msgstr "空闲超时 "

msgid "Hop Interval"
msgstr "端口跳跃时间 "

msgid "HeartbeatPeriod(second)"
msgstr "心跳周期（单位：秒）"

msgid "Override the connection destination address"
msgstr "覆盖连接目标地址"

msgid "Handshake Server"
msgstr "握手服务器"

msgid "Handshake Server Port"
msgstr "握手服务器端口"

msgid "Override the connection destination address with the sniffed domain.<br />Otherwise use sniffed domain for routing only.<br />If using shunt nodes, configure the domain shunt rules correctly."
msgstr "用探测出的域名覆盖连接目标地址。<br />否则仅将探测得到的域名用于路由。<br />如使用分流节点，请正确设置域名分流规则。"

msgid "Override the connection destination address with the sniffed domain.<br />When enabled, traffic will match only by domain, ignoring IP rules.<br />If using shunt nodes, configure the domain shunt rules correctly."
msgstr "用探测出的域名覆盖连接目标地址。<br />启用后仅使用域名进行流量匹配，将忽略IP规则。<br />如使用分流节点，请正确设置域名分流规则。"

msgid "Protocol parameter. Will waste traffic randomly if enabled."
msgstr "协议参数。如果启用会随机浪费流量。"

msgid "Protocol parameter. Enable length block encryption."
msgstr "协议参数。启用长度块加密。"

msgid "ECH Config"
msgstr "ECH 密钥"

msgid "ECH Key"
msgstr "ECH 配置"

msgid "PQ signature schemes"
msgstr "后量子对等证书签名方案"

msgid "Disable adaptive sizing of TLS records"
msgstr "禁用 TLS 记录的自适应大小调整"

msgid "Enable Multipath TCP, need to be enabled in both server and client configuration."
msgstr "启用 Multipath TCP，需在服务端和客户端配置中同时启用。"

msgid "Fragment"
msgstr "分片"

msgid "TCP fragments, which can deceive the censorship system in some cases, such as bypassing SNI blacklists."
msgstr "TCP 分片，在某些情况下可以欺骗审查系统，比如绕过 SNI 黑名单。"

msgid "Fragment Packets"
msgstr "分片方式"

msgid "\"1-3\" is for segmentation at TCP layer, applying to the beginning 1 to 3 data writes by the client. \"tlshello\" is for TLS client hello packet fragmentation."
msgstr "\"1-3\" 是 TCP 的流切片，应用于客户端第 1 至第 3 次写数据。\"tlshello\" 是 TLS 握手包切片。"

msgid "Fragment Length"
msgstr "分片包长"

msgid "Fragmented packet length (byte)"
msgstr "分片包长 (byte)"

msgid "Fragment Interval"
msgstr "分片间隔"

msgid "Fragmentation interval (ms)"
msgstr "分片间隔（ms）"

msgid "Noise"
msgstr "噪声"

msgid "UDP noise, Under some circumstances it can bypass some UDP based protocol restrictions."
msgstr "UDP 噪声，在某些情况下可以绕过一些针对 UDP 协议的限制。"

msgid "To send noise packets, select \"Noise\" in Xray Settings."
msgstr "在 Xray 设置中勾选 “噪声” 以发送噪声包。"

msgid "Xray Noise Packets"
msgstr "Xray 噪声数据包"

msgid "Packet"
msgstr "数据包"

msgid "Delay (ms)"
msgstr "延迟（ms）"

msgid "If is domain name, The requested domain name will be resolved to IP before connect."
msgstr "如果是域名，域名将在请求发出之前解析为 IP。"

msgid "Chain Proxy"
msgstr "链式代理"

msgid "Landing Node"
msgstr "落地节点"

msgid "Only support a layer of proxy."
msgstr "仅支持一层代理。"

msgid "Only work with using the %s node."
msgstr "与使用 %s 节点时生效。"

msgid "Set the default domain resolution strategy for the sing-box node."
msgstr "为 sing-box 节点设置默认的域名解析策略。"

msgid "Total Lines"
msgstr "总行数："

msgid "Read List"
msgstr "读取列表"

msgid "Maintain"
msgstr "维护"

msgid "Backup and Restore"
msgstr "备份还原"

msgid "Backup or Restore Client and Server Configurations."
msgstr "备份或还原客户端及服务端配置。"

msgid "Note: Restoring configurations across different versions may cause compatibility issues."
msgstr "注意：不同版本间的配置恢复可能会导致兼容性问题。"

msgid "Create Backup File"
msgstr "创建备份文件"

msgid "Restore Backup File"
msgstr "恢复备份文件"

msgid "DL Backup"
msgstr "下载备份"

msgid "RST Backup"
msgstr "恢复备份"

msgid "UL Restore"
msgstr "上传恢复"

msgid "CLOSE WIN"
msgstr "关闭窗口"

msgid "Restore to default configuration"
msgstr "恢复默认配置"

msgid "Do Reset"
msgstr "执行重置"

msgid "Please select a file first."
msgstr "请先选择一个文件。"

msgid "Invalid file type. Please upload a .tar.gz file."
msgstr "文件类型无效，请上传一个 .tar.gz 文件。"

msgid "File size exceeds 10MB limit."
msgstr "文件大小超过 10MB 限制。"

msgid "Do you want to restore the client to default settings?"
msgstr "是否要恢复客户端默认配置？"

msgid "Are you sure you want to restore the client to default settings?"
msgstr "是否真的要恢复客户端默认配置？"

msgid "Settings have been successfully saved and applied!"
msgstr "设置已成功保存并应用!"

msgid "_urltest"
msgstr "URLTest"

msgid "URLTest node list"
msgstr "URLTest 节点列表"

msgid "List of nodes to test, <a target='_blank' href='https://sing-box.sagernet.org/configuration/outbound/urltest'>document</a>"
msgstr "要测试的节点列表，<a target='_blank' href='https://sing-box.sagernet.org/zh/configuration/outbound/urltest'>文档原理</a>"

msgid "Test interval"
msgstr "测试间隔"

msgid "Test interval must be less or equal than idle timeout."
msgstr "测试间隔时间必须小于或等于空闲超时时间。"

msgid "Test tolerance"
msgstr "测试容差"

msgid "The test tolerance in milliseconds."
msgstr "测试容差时间（单位：毫秒）。"

msgid "Idle timeout"
msgstr "空闲超时"

msgid "The idle timeout."
msgstr "空闲超时时间。"

msgid "Interrupt existing connections"
msgstr "中断现有连接"

msgid "Interrupt existing connections when the selected outbound has changed."
msgstr "当选择的出站发生变化时中断现有连接。"

msgid "Port hopping range"
msgstr "端口跳跃范围"

msgid "Format as 1000:2000 or 1000-2000 Multiple groups are separated by commas (,)."
msgstr "格式为：1000:2000 或 1000-2000 多组时用逗号(,)隔开。"

msgid "Use Custom Config"
msgstr "使用自定义配置"

msgid "Custom Config"
msgstr "自定义配置"

msgid "Must be JSON text!"
msgstr "必须是 JSON 文本内容！"

msgid "Geo View"
msgstr "Geo 查询"

msgid "Query"
msgstr "查询"

msgid "Querying"
msgstr "查询中"

msgid "Please enter query content!"
msgstr "请输入查询内容！"

msgid "No results were found!"
msgstr "未找到任何结果！"

msgid "Domain/IP Query"
msgstr "域名/IP 查询"

msgid "GeoIP/Geosite Query"
msgstr "GeoIP/Geosite 查询"

msgid "Enter a domain or IP to query the Geo rule list they belong to."
msgstr "输入域名/IP，查询它们所在的 Geo 规则列表。"

msgid "Enter a GeoIP or Geosite to extract the domains/IPs they contain. Format: geoip:cn or geosite:gfw"
msgstr "输入 GeoIP/Geosite，提取它们所包含的域名/IP。格式：geoip:cn 或 geosite:gfw"

msgid "Tips:"
msgstr "小贴士："

msgid "By entering a domain or IP, you can query the Geo rule list they belong to."
msgstr "可以通过输入域名/IP，查询它们所在的 Geo 规则列表。"

msgid "By entering a GeoIP or Geosite, you can extract the domains/IPs they contain."
msgstr "可以通过输入 GeoIP/Geosite，提取它们所包含的域名/IP。"

msgid "Use the GeoIP/Geosite query function to verify if the entered Geo rules are correct."
msgstr "利用 GeoIP/Geosite 查询功能，可以验证输入的 Geo 规则是否正确。"
