﻿msgid "Pass Wall"
msgstr "xqnetwork"

msgid "Auto"
msgstr "鑷姩"

msgid "RUNNING"
msgstr "杩愯涓?

msgid "NOT RUNNING"
msgstr "鏈繍琛?

msgid "Working..."
msgstr "杩炴帴姝ｅ父"

msgid "Problem detected!"
msgstr "杩炴帴澶辫触"

msgid "Touch Check"
msgstr "鐐规垜妫€娴?

msgid "Kernel Unsupported"
msgstr "鍐呮牳涓嶆敮鎸?

msgid "Settings"
msgstr "璁剧疆"

msgid "Main Settings"
msgstr "鑺傜偣閫夋嫨"

msgid "Basic Settings"
msgstr "鍩烘湰璁剧疆"

msgid "Node List"
msgstr "鑺傜偣鍒楄〃"

msgid "Other Settings"
msgstr "楂樼骇璁剧疆"

msgid "Load Balancing"
msgstr "璐熻浇鍧囪　"

msgid "Enter interface"
msgstr "杩涘叆鐣岄潰"

msgid "Rule Manage"
msgstr "瑙勫垯绠＄悊"

msgid "Rule List"
msgstr "瑙勫垯鍒楄〃"

msgid "Access control"
msgstr "璁块棶鎺у埗"

msgid "Watch Logs"
msgstr "鏌ョ湅鏃ュ織"

msgid "Node Config"
msgstr "鑺傜偣閰嶇疆"

msgid "Running Status"
msgstr "杩愯鐘舵€?

msgid "Baidu Connection"
msgstr "鐧惧害杩炴帴"

msgid "Google Connection"
msgstr "璋锋瓕杩炴帴"

msgid "GitHub Connection"
msgstr "GitHub 杩炴帴"

msgid "Instagram Connection"
msgstr "Instagram 杩炴帴"

msgid "Node Check"
msgstr "鑺傜偣妫€娴?

msgid "Check..."
msgstr "妫€娴嬩腑..."

msgid "Clear"
msgstr "娓呴櫎"

msgid "Main switch"
msgstr "涓诲紑鍏?

msgid "TCP Node"
msgstr "TCP 鑺傜偣"

msgid "UDP Node"
msgstr "UDP 鑺傜偣"

msgid "Edit Current Node"
msgstr "缂栬緫褰撳墠鑺傜偣"

msgid "Socks Config"
msgstr "Socks 閰嶇疆"

msgid "Socks Node"
msgstr "Socks 鑺傜偣"

msgid "Listen Port"
msgstr "鐩戝惉绔彛"

msgid "0 is not use"
msgstr "0 涓轰笉浣跨敤"

msgid "Same as the tcp node"
msgstr "涓?TCP 鑺傜偣鐩稿悓"

msgid "Current node: %s"
msgstr "褰撳墠鑺傜偣锛?s"

msgid "DNS Shunt"
msgstr "DNS鍒嗘祦"

msgid "Domestic group name"
msgstr "鍥藉唴鍒嗙粍鍚?

msgid "You only need to configure domestic DNS packets in SmartDNS and set it redirect or as Dnsmasq upstream, and fill in the domestic DNS group name here."
msgstr "鎮ㄥ彧闇€瑕佸湪SmartDNS閰嶇疆濂藉浗鍐匘NS鍒嗙粍锛屽苟璁剧疆閲嶅畾鍚戞垨浣滀负Dnsmasq涓婃父锛屾澶勫～鍏ュ浗鍐匘NS鍒嗙粍鍚嶃€?

msgid "Filter Mode"
msgstr "杩囨护妯″紡"

msgid "If the node uses Xray/Sing-Box shunt, select the matching filter mode (Xray/Sing-Box)."
msgstr "褰撹妭鐐逛娇鐢?Xray/Sing-Box 鍒嗘祦鏃讹紝杩囨护妯″紡闇€瀵瑰簲閫夋嫨 Xray/Sing-Box 銆?

msgid "A/AAAA type"
msgstr "A/AAAA 绫诲瀷"

msgid "TCP node must be '%s' type to use FakeDNS."
msgstr "TCP 鑺傜偣蹇呴』鏄?'%s' 绫诲瀷鎵嶈兘浣跨敤 FakeDNS銆?

msgid "Direct DNS"
msgstr "鐩磋繛 DNS"

msgid "Direct DNS DoT"
msgstr "鐩磋繛 DNS DoT"

msgid "Remote DNS"
msgstr "杩滅▼ DNS"

msgid "Resolver For The List Proxied"
msgstr "瑙ｆ瀽琚唬鐞嗙殑鍩熷悕鍒楄〃"

msgid "Requery DNS By %s"
msgstr "閫氳繃 %s 璇锋眰 DNS"

msgid "Socks Server"
msgstr "Socks 鏈嶅姟鍣?

msgid "Misconfigured"
msgstr "閰嶇疆涓嶅綋"

msgid "Make sure socks service is available on this address."
msgstr "璇风‘淇濇 Socks 鏈嶅姟鍙敤銆?

msgid "%s request address"
msgstr "%s 璇锋眰鍦板潃"

msgid "Format must be:"
msgstr "鏍煎紡蹇呴』涓猴細"

msgid "Request protocol"
msgstr "璇锋眰鍗忚"

msgid "Remote DNS DoH"
msgstr "杩滅▼ DNS DoH"

msgid "Remote DNS DoT"
msgstr "杩滅▼ DNS DoT"

msgid "Notify the DNS server when the DNS query is notified, the location of the client (cannot be a private IP address)."
msgstr "鐢ㄤ簬 DNS 鏌ヨ鏃堕€氱煡 DNS 鏈嶅姟鍣紝瀹㈡埛绔墍鍦ㄧ殑鍦扮悊浣嶇疆锛堜笉鑳芥槸绉佹湁 IP 鍦板潃锛夈€?

msgid "This feature requires the DNS server to support the Edns Client Subnet (RFC7871)."
msgstr "姝ゅ姛鑳介渶瑕?DNS 鏈嶅姟鍣ㄦ敮鎸?EDNS Client Subnet锛圧FC7871锛夈€?

msgid "The effect is better, recommend."
msgstr "鏁堟灉鏇村ソ锛屾帹鑽愪娇鐢ㄣ€?

msgid "ChinaDNS-NG (recommended)"
msgstr "ChinaDNS-NG (鎺ㄨ崘)"

msgid "Default DNS"
msgstr "榛樿 DNS"

msgid "When not matching any domain name list:"
msgstr "褰撲笉鍖归厤浠讳綍鍩熷悕鍒楄〃鏃讹細"

msgid "Remote DNS: Can avoid more DNS leaks, but some domestic domain names maybe to proxy!"
msgstr "杩滅▼ DNS锛氬彲浠ラ伩鍏嶆洿澶氱殑 DNS 娉勯湶锛屼絾浼氬鑷磋鍒欏垪琛ㄥ鐨勬煇浜涘浗鍐呭煙鍚嶅彲鑳戒細璧颁唬鐞嗭紒"

msgid "Direct DNS: Internet experience may be better, but DNS will be leaked!"
msgstr "鐩磋繛 DNS锛氫笂缃戜綋楠屽彲鑳戒細鏇翠匠锛屼絾鏄細娉勯湶 DNS锛?

msgid "Smart, Do not accept no-ip reply from Direct DNS"
msgstr "鏅鸿兘锛屼笉鎺ュ彈鐩磋繛 DNS 绌哄搷搴?

msgid "Smart, Accept no-ip reply from Direct DNS"
msgstr "鏅鸿兘锛屾帴鍙楃洿杩?DNS 绌哄搷搴?

msgid "Smart: Forward to both direct and remote DNS, if the direct DNS resolution result is a mainland China IP, then use the direct result, otherwise use the remote result."
msgstr "鏅鸿兘锛氬悓鏃惰浆鍙戠粰鐩磋繛鍜岃繙绋?DNS锛屽鏋滅洿杩?DNS 瑙ｆ瀽缁撴灉鏄ぇ闄?IP锛屽垯浣跨敤鐩磋繛缁撴灉锛屽惁鍒欎娇鐢ㄨ繙绋嬬粨鏋溿€?

msgid "In smart mode, no-ip reply from Direct DNS:"
msgstr "浣跨敤鏅鸿兘妯″紡锛岀洿杩?DNS 杩斿洖绌哄搷搴旀椂:"

msgid "Do not accept: Wait and use Remote DNS Reply."
msgstr "涓嶆帴鍙楋細绛夊緟骞朵娇鐢ㄨ繙绋?DNS 鐨勫搷搴斻€?

msgid "Accept: Trust the Reply, using this option can improve DNS resolution speeds for some mainland IPv4-only sites."
msgstr "鎺ュ彈锛氫俊浠荤┖鍝嶅簲锛屼娇鐢ㄦ閫夐」鍙互鎻愬崌閮ㄥ垎澶ч檰浠?IPv4 绔欑偣鐨?DNS 瑙ｆ瀽閫熷害銆?

msgid "Filter Proxy Host IPv6"
msgstr "杩囨护浠ｇ悊鍩熷悕 IPv6"

msgid "Experimental feature."
msgstr "瀹為獙鎬у姛鑳姐€?

msgid "Use FakeDNS work in the shunt domain that proxy."
msgstr "闇€瑕佷唬鐞嗙殑鍒嗘祦瑙勫垯鍩熷悕浣跨敤 FakeDNS銆?

msgid "Redirect"
msgstr "閲嶅畾鍚?

msgid "DNS Redirect"
msgstr "DNS 閲嶅畾鍚?

msgid "Force special DNS server to need proxy devices."
msgstr "寮哄埗闇€瑕佷唬鐞嗙殑璁惧浣跨敤涓撶敤 DNS 鏈嶅姟鍣ㄣ€?

msgid "Clear IPSET"
msgstr "娓呯┖ IPSET"

msgid "Clear NFTSET"
msgstr "娓呯┖ NFTSET"

msgid "DoT Cert verify"
msgstr "DoT 璇佷功楠岃瘉"

msgid "Verify DoT SSL cert. (May fail on some platforms!)"
msgstr "楠岃瘉 DoT SSL 璇佷功銆傦紙鍦ㄦ煇浜涘钩鍙板彲鑳芥棤娉曢獙璇侊紝璋ㄦ厧寮€鍚紒锛?

msgid "Try this feature if the rule modification does not take effect."
msgstr "濡傛灉淇敼瑙勫垯鍚庢病鏈夌敓鏁堬紝璇峰皾璇曟鍔熻兘銆?

msgid "Force HTTPS SOA"
msgstr "鍋滅敤 HTTPS 璁板綍瑙ｆ瀽"

msgid "Force queries with qtype 65 to respond with an SOA record."
msgstr "寮哄埗浣?qtype 65 鏌ヨ杩斿洖 SOA銆?

msgid "Are you sure to hide?"
msgstr "浣犵‘瀹氳闅愯棌鍚楋紵"

msgid "DNS related issues:"
msgstr "DNS 鐩稿叧闂锛?

msgid "Certain browsers such as Chrome have built-in DNS service, which may affect DNS resolution settings. You can go to 'Settings -> Privacy and security -> Use secure DNS' menu to turn it off."
msgstr "鏌愪簺娴忚鍣ㄥ Chrome 绛夊唴缃鍔熻兘锛岃繖浼氬奖鍝嶈缃殑 DNS 瑙ｆ瀽銆傛偍鍙互杩涘叆 '璁剧疆->绉侀殣鍜屽畨鍏?>浣跨敤瀹夊叏 DNS' 鑿滃崟,灏嗗叾鍏抽棴銆?

msgid "If you are unable to access the internet after reboot, please try clearing the cache of your terminal devices (make sure to close all open browser application windows first, this step is especially important):"
msgstr "濡傛灉鍦ㄩ噸鍚悗鏃犳硶涓婄綉锛岃灏濊瘯娓呴櫎缁堢璁惧鐨勭紦瀛橈紙鍏堝叧闂墍鏈夋鍦ㄤ娇鐢ㄧ殑娴忚鍣ㄥ簲鐢ㄧ▼搴忥紝杩欎竴姝ュ挨鍏堕噸瑕侊級锛?

msgid "For Windows systems, open Command Prompt and run the command 'ipconfig /flushdns'."
msgstr "瀵逛簬Windows绯荤粺锛岃鍦ㄥ懡浠ゆ彁绀虹涓繍琛屽懡浠?'ipconfig /flushdns'."

msgid "For Mac systems, open Terminal and run the command 'sudo killall -HUP mDNSResponder'."
msgstr "瀵逛簬Mac绯荤粺锛屽湪缁堢涓繍琛屽懡浠?'sudo killall -HUP mDNSResponder'."

msgid "For mobile devices, you can clear it by reconnecting to the network, such as toggling Airplane Mode and reconnecting to WiFi."
msgstr "瀵逛簬绉诲姩璁惧锛屽彲閫氳繃閲嶆柊鎺ュ叆缃戠粶鐨勬柟寮忔竻闄ゃ€傛瘮濡傚紑鍏充竴娆￠琛屾ā寮忥紝閲嶆柊杩炴帴 WiFi銆?

msgid "Please make sure your device's network settings point both the DNS server and default gateway to this router, to ensure DNS queries are properly routed."
msgstr "璇风‘璁ゆ偍璁惧鐨勭綉缁滆缃紝瀹㈡埛绔?DNS 鏈嶅姟鍣ㄥ拰榛樿缃戝叧搴斿潎鎸囧悜鏈矾鐢卞櫒锛屼互纭繚 DNS 鏌ヨ姝ｇ‘璺敱銆?

msgid "Browser access:"
msgstr "娴忚鍣ㄨ闂細"

msgid "Hide in main menu:"
msgstr "鍦ㄤ富鑿滃崟涓殣钘忥細"

msgid "Show in main menu:"
msgstr "鍦ㄤ富鑿滃崟涓樉绀猴細"

msgid "DNS Export Of Multi WAN"
msgstr "鍥藉唴 DNS 鎸囧畾瑙ｆ瀽鍑哄彛"

msgid "Node Export Of Multi WAN"
msgstr "鑺傜偣鎸囧畾鍑哄彛"

msgid "Only support Multi Wan."
msgstr "鍙湁澶氱嚎鎺ュ叆鎵嶆湁鏁堛€?

msgid "Not Specify"
msgstr "涓嶆寚瀹?

msgid "custom"
msgstr "鑷畾涔?

msgid "Process"
msgstr "杩涚▼"

msgid "1 Process"
msgstr "鍗曡繘绋?

msgid "Use %s"
msgstr "浣跨敤 %s"

msgid "Close(Not use)"
msgstr "鍏抽棴锛堜笉浣跨敤锛?

msgid "Proxy Mode"
msgstr "浠ｇ悊妯″紡"

msgid "Default Proxy Mode"
msgstr "榛樿浠ｇ悊妯″紡"

msgid "No Proxy"
msgstr "涓嶄唬鐞?

msgid "Proxy"
msgstr "浠ｇ悊"

msgid "Global Proxy"
msgstr "鍏ㄥ眬浠ｇ悊"

msgid "GFW List"
msgstr "GFW 鍒楄〃"

msgid "Not China List"
msgstr "涓浗鍒楄〃浠ュ"

msgid "China List"
msgstr "涓浗鍒楄〃"

msgid "Localhost"
msgstr "鏈満"

msgid "Switch Mode"
msgstr "鍒囨崲妯″紡"

msgid "Localhost Proxy"
msgstr "璺敱鍣ㄦ湰鏈轰唬鐞?

msgid "When selected, localhost can transparent proxy."
msgstr "褰撳嬀閫夋椂锛岃矾鐢卞櫒鏈満鍙互閫忔槑浠ｇ悊銆?

msgid "Client Proxy"
msgstr "瀹㈡埛绔唬鐞?

msgid "When selected, devices in LAN can transparent proxy. Otherwise, it will not be proxy. But you can still use access control to allow the designated device to proxy."
msgstr "褰撳嬀閫夋椂锛屽眬鍩熺綉鍐呯殑璁惧鍙互閫忔槑浠ｇ悊銆傚惁鍒欙紝灏嗕笉浠ｇ悊銆備絾鎮ㄤ粛鐒跺彲浠ヤ娇鐢ㄨ闂帶鍒跺厑璁告寚瀹氱殑璁惧浠ｇ悊銆?

msgid "Want different devices to use different proxy modes/ports/nodes? Please use access control."
msgstr "甯屾湜涓嶅悓璁惧浣跨敤涓嶅悓鐨勪唬鐞嗘ā寮?绔彛/鑺傜偣锛熻浣跨敤璁块棶鎺у埗銆?

msgid "Operation"
msgstr "鎿嶄綔"

msgid "Add Node"
msgstr "娣诲姞鑺傜偣"

msgid "Add the node via the link"
msgstr "閫氳繃閾炬帴娣诲姞鑺傜偣"

msgid "Enter share links, one per line. Subscription links are not supported!"
msgstr "杈撳叆鍒嗕韩閾炬帴锛屾敮鎸佸涓妭鐐癸紝姣忚涓€涓€傝鍕胯緭鍏ヨ闃呴摼鎺ワ紒"

msgid "Please enter the correct link."
msgstr "璇疯緭鍏ユ纭殑閾炬帴銆?

msgid "Clear all nodes"
msgstr "娓呯┖鎵€鏈夎妭鐐?

msgid "Are you sure to clear all nodes?"
msgstr "浣犵‘瀹氳娓呯┖鎵€鏈夎妭鐐瑰悧锛?

msgid "Error"
msgstr "閿欒"

msgid "Delete select nodes"
msgstr "鍒犻櫎閫夋嫨鐨勮妭鐐?

msgid "To Top"
msgstr "缃《"

msgid "Select"
msgstr "閫夋嫨"

msgid "DeSelect"
msgstr "鍙嶉€?

msgid "Select all"
msgstr "鍏ㄩ€?

msgid "DeSelect all"
msgstr "鍏ㄤ笉閫?

msgid "Are you sure to delete select nodes?"
msgstr "浣犵‘瀹氳鍒犻櫎閫夋嫨鐨勮妭鐐瑰悧锛?

msgid "You no select nodes !"
msgstr "浣犳病鏈夐€夋嫨浠讳綍鑺傜偣锛?

msgid "Are you sure set to"
msgstr "浣犵‘瀹氳璁句负"

msgid "the server?"
msgstr "鏈嶅姟鍣ㄥ悧锛?

msgid "You choose node is:"
msgstr "浣犻€夋嫨鐨勮妭鐐规槸锛?

msgid "Timeout"
msgstr "瓒呮椂"

msgid "Node Remarks"
msgstr "鑺傜偣澶囨敞"

msgid "Add Mode"
msgstr "娣诲姞鏂瑰紡"

msgid "Type"
msgstr "绫诲瀷"

msgid "_balancing"
msgstr "璐熻浇鍧囪　"

msgid "_shunt"
msgstr "鍒嗘祦"

msgid "_iface"
msgstr "鎺ュ彛"

msgid "Balancing"
msgstr "璐熻浇鍧囪　"

msgid "Balancing Strategy"
msgstr "璐熻浇鍧囪　绛栫暐"

msgid "Fallback Node"
msgstr "鍚庡鑺傜偣"

msgid "Use Custome Probe URL"
msgstr "浣跨敤鑷畾涔夋帰娴嬬綉鍧€"

msgid "By default the built-in probe URL will be used, enable this option to use a custom probe URL."
msgstr "榛樿浣跨敤鍐呯疆鐨勬帰娴嬬綉鍧€锛屽惎鐢ㄦ閫夐」浠ヤ娇鐢ㄨ嚜瀹氫箟鎺㈡祴缃戝潃銆?

msgid "Probe URL"
msgstr "鎺㈡祴缃戝潃"

msgid "The URL used to detect the connection status."
msgstr "鐢ㄤ簬妫€娴嬭繛鎺ョ姸鎬佺殑缃戝潃銆?

msgid "Probe Interval"
msgstr "鎺㈡祴闂撮殧"

msgid "The interval between initiating probes."
msgstr "鍙戣捣鎺㈡祴鐨勯棿闅斻€?

msgid "The time format is numbers + units, such as '10s', '2h45m', and the supported time units are <code>s</code>, <code>m</code>, <code>h</code>, which correspond to seconds, minutes, and hours, respectively."
msgstr "鏃堕棿鏍煎紡涓烘暟瀛?鍗曚綅锛屾瘮濡?code>&quot;10s&quot;</code>, <code>&quot;2h45m&quot;</code>锛屾敮鎸佺殑鏃堕棿鍗曚綅鏈?<code>s</code>锛?code>m</code>锛?code>h</code>锛屽垎鍒搴旂銆佸垎銆佹椂銆?

msgid "When the unit is not filled in, it defaults to seconds."
msgstr "鏈～鍐欏崟浣嶆椂锛岄粯璁や负绉掋€?

msgid "Preferred Node Count"
msgstr "浼橀€夎妭鐐规暟閲?

msgid "The load balancer selects the optimal number of nodes, and traffic is randomly distributed among them."
msgstr "璐熻浇鍧囪　鍣ㄩ€夊嚭鏈€浼樿妭鐐圭殑涓暟锛屾祦閲忓皢鍦ㄨ繖鍑犱釜鑺傜偣涓殢鏈哄垎閰嶃€?

msgid "Shunt"
msgstr "鍒嗘祦"

msgid "Preproxy"
msgstr "鍓嶇疆浠ｇ悊"

msgid "Preproxy Node"
msgstr "鍓嶇疆浠ｇ悊鑺傜偣"

msgid "Set the node to be used as a pre-proxy. Each rule (including <code>Default</code>) has a separate switch that controls whether this rule uses the pre-proxy or not."
msgstr "璁剧疆鐢ㄤ綔鍓嶇疆浠ｇ悊鐨勮妭鐐广€傛瘡鏉¤鍒欙紙鍖呮嫭<code>榛樿</code>锛夐兘鏈夌嫭绔嬪紑鍏虫帶鍒舵湰瑙勫垯鏄惁浣跨敤鍓嶇疆浠ｇ悊銆?

msgid "Direct Connection"
msgstr "鐩磋繛"

msgid "Blackhole"
msgstr "榛戞礊"

msgid "Default Preproxy"
msgstr "榛樿鍓嶇疆浠ｇ悊"

msgid "There are no available nodes, please add or subscribe nodes first."
msgstr "娌℃湁鍙敤鑺傜偣锛岃鍏堟坊鍔犳垨璁㈤槄鑺傜偣銆?

msgid "No shunt rules? Click me to go to add."
msgstr "娌℃湁鍒嗘祦瑙勫垯锛熺偣鎴戝墠寰€鍘绘坊鍔犮€?

msgid "When using, localhost will connect this node first and then use this node to connect the default node."
msgstr "褰撲娇鐢ㄦ椂锛屾湰鏈哄皢棣栧厛杩炴帴鍒版鑺傜偣锛岀劧鍚庡啀浣跨敤姝よ妭鐐硅繛鎺ュ埌榛樿鑺傜偣钀藉湴銆?

msgid "Domain Strategy"
msgstr "鍩熷悕瑙ｆ瀽绛栫暐"

msgid "Domain matcher"
msgstr "鍩熷悕鍖归厤绠楁硶"

msgid "'AsIs': Only use domain for routing. Default value."
msgstr "AsIs锛氬彧浣跨敤鍩熷悕杩涜璺敱閫夋嫨銆傞粯璁ゅ€笺€?

msgid "'IPIfNonMatch': When no rule matches current domain, resolves it into IP addresses (A or AAAA records) and try all rules again."
msgstr "IPIfNonMatch锛氬綋鍩熷悕娌℃湁鍖归厤浠讳綍瑙勫垯鏃讹紝灏嗗煙鍚嶈В鏋愭垚 IP锛圓 璁板綍鎴?AAAA 璁板綍锛夊啀娆¤繘琛屽尮閰嶃€?

msgid "'IPOnDemand': As long as there is a IP-based rule, resolves the domain into IP immediately."
msgstr "IPOnDemand锛氬綋鍖归厤鏃剁鍒颁换浣曞熀浜?IP 鐨勮鍒欙紝灏嗗煙鍚嶇珛鍗宠В鏋愪负 IP 杩涜鍖归厤銆?

msgid "Prefer IPv4"
msgstr "IPv4 浼樺厛"

msgid "Prefer IPv6"
msgstr "IPv6 浼樺厛"

msgid "IPv4 Only"
msgstr "浠?IPv4"

msgid "IPv6 Only"
msgstr "浠?IPv6"

msgid "Load balancing node list"
msgstr "璐熻浇鍧囪　鑺傜偣鍒楄〃"

msgid "Load balancing node list, <a target='_blank' href='https://xtls.github.io/config/routing.html#balancerobject'>document</a>"
msgstr "璐熻浇鍧囪　鑺傜偣鍒楄〃锛?a target='_blank' href='https://xtls.github.io/config/routing.html#balancerobject'>鏂囨。鍘熺悊</a>"

msgid "From Share URL"
msgstr "瀵煎叆鍒嗕韩URL"

msgid "Build Share URL"
msgstr "瀵煎嚭鍒嗕韩URL"

msgid "Generate QRCode"
msgstr "鐢熸垚浜岀淮鐮?

msgid "Export Config File"
msgstr "瀵煎嚭閰嶇疆鏂囦欢"

msgid "Import Finished"
msgstr "瀵煎叆瀹屾垚锛?

msgid "Not a supported scheme:"
msgstr "涓嶆敮鎸佽繖绉嶆牱寮忕殑锛?

msgid "Invalid Share URL Format"
msgstr "鏃犳晥鐨勫垎浜玌RL淇℃伅"

msgid "Paste Share URL Here"
msgstr "鍦ㄦ澶勭矘璐村垎浜俊鎭?

msgid "Share URL to clipboard unable."
msgstr "鏃犳硶鍒嗕韩URL鍒板壀璐存澘銆?

msgid "Share URL to clipboard successfully."
msgstr "鎴愬姛澶嶅埗鍒嗕韩URL鍒板壀璐存澘銆?

msgid "Faltal on get option, please help in debug:"
msgstr "浠ｇ爜閿欒锛岃鍗忓姪鎹夎櫕锛?

msgid "Faltal on set option, please help in debug:"
msgstr "浠ｇ爜閿欒锛岃鍗忓姪鎹夎櫕锛?

msgid "Address"
msgstr "鍦板潃"

msgid "Address (Support Domain Name)"
msgstr "鍦板潃锛堟敮鎸佸煙鍚嶏級"

msgid "Trojan Verify Cert"
msgstr "楠岃瘉璇佷功"

msgid "Trojan Cert Path"
msgstr "璇佷功璺緞"

msgid "Finger Print"
msgstr "鎸囩汗浼€?

msgid "Avoid using randomized, unless you have to."
msgstr "閬垮厤浣跨敤 randomized , 闄ら潪浣犲繀椤昏銆?

msgid "Original"
msgstr "鍘熺増"

msgid "Transport Plugin"
msgstr "浼犺緭灞傛彃浠?

msgid "Shadowsocks secondary encryption"
msgstr "Shadowsocks 浜屾鍔犲瘑"

msgid "Obfs Type"
msgstr "娣锋穯绫诲瀷"

msgid "Obfs Password"
msgstr "娣锋穯瀵嗙爜"

msgid "Auth Type"
msgstr "璁よ瘉绫诲瀷"

msgid "Auth Password"
msgstr "璁よ瘉瀵嗙爜"

msgid "Commands the client to use the BBR flow control algorithm"
msgstr "鍛戒护瀹㈡埛绔娇鐢?BBR 娴侀噺鎺у埗绠楁硶"

msgid "PinSHA256"
msgstr "PinSHA256"

msgid "Certificate fingerprint"
msgstr "璇佷功鎸囩汗"

msgid "Max upload Mbps"
msgstr "鏈€澶т笂琛?Mbps)"

msgid "Max download Mbps"
msgstr "鏈€澶т笅琛?Mbps)"

msgid "QUIC stream receive window"
msgstr "QUIC 娴佹帴鏀剁獥鍙?

msgid "QUIC connection receive window"
msgstr "QUIC 杩炴帴鎺ユ敹绐楀彛"

msgid "QUIC concurrent bidirectional streams"
msgstr "QUIC 骞跺彂鍙屽悜娴佺殑鏈€澶ф暟閲?

msgid "Disable MTU detection"
msgstr "绂佺敤 MTU 妫€娴?

msgid "ignoreClientBandwidth"
msgstr "蹇界暐瀹㈡埛绔甫瀹借缃?

msgid "Lazy Start"
msgstr "寤惰繜鍚姩"

msgid "Encrypt Method"
msgstr "鍔犲瘑"

msgid "Latency"
msgstr "寤惰繜"

msgid "Automatic detection delay"
msgstr "鑷姩妫€娴嬪欢杩?

msgid "Show server address and port"
msgstr "鏄剧ず鏈嶅姟鍣ㄥ湴鍧€鍜岀鍙?

msgid "URL Test"
msgstr "URL 娴嬭瘯"

msgid "Test"
msgstr "娴嬭瘯"

msgid "Node num"
msgstr "鑺傜偣鏁伴噺"

msgid "Self add"
msgstr "鑷坊"

msgid "Apply"
msgstr "搴旂敤"

msgid "Use"
msgstr "浣跨敤"

msgid "Copy"
msgstr "澶嶅埗"

msgid "Delay Settings"
msgstr "瀹氭椂閰嶇疆"

msgid "Open and close Daemon"
msgstr "鍚姩瀹堟姢杩涚▼"

msgid "Delay Start"
msgstr "寮€鏈烘椂寤舵椂鍚姩"

msgid "Units:seconds"
msgstr "鍗曚綅锛氱"

msgid "Units:minutes"
msgstr "鍗曚綅锛氬垎閽?

msgid "stop automatically mode"
msgstr "瀹氭椂鍏抽棴妯″紡"

msgid "stop Time(Every day)"
msgstr "鍏抽棴鏃堕棿(姣忓ぉ)"

msgid "stop Interval(Hour)"
msgstr "鍏抽棴闂撮殧(灏忔椂)"

msgid "start automatically mode"
msgstr "瀹氭椂寮€鍚ā寮?

msgid "start Time(Every day)"
msgstr "寮€鍚椂闂?姣忓ぉ)"

msgid "start Interval(Hour)"
msgstr "寮€鍚棿闅?灏忔椂)"

msgid "restart automatically mode"
msgstr "瀹氭椂閲嶅惎妯″紡"

msgid "restart Time(Every day)"
msgstr "閲嶅惎鏃堕棿(姣忓ぉ)"

msgid "restart Interval(Hour)"
msgstr "閲嶅惎闂撮殧(灏忔椂)"

msgid "Forwarding Settings"
msgstr "杞彂閰嶇疆"

msgid "TCP No Redir Ports"
msgstr "TCP 涓嶈浆鍙戠鍙?

msgid "UDP No Redir Ports"
msgstr "UDP 涓嶈浆鍙戠鍙?

msgid "Fill in the ports you don't want to be forwarded by the agent, with the highest priority."
msgstr "濉啓浣犱笉甯屾湜琚唬鐞嗚浆鍙戠殑绔彛锛屼紭鍏堢骇鏈€楂樸€?

msgid "The port settings support single ports and ranges.<br>Separate multiple ports with commas (,).<br>Example: 21,80,443,1000:2000."
msgstr "浠ヤ笂绔彛璁剧疆鏀寔鍗曠鍙ｅ拰绔彛鑼冨洿銆?br>澶氫釜绔彛鐢ㄨ嫳鏂囬€楀彿(,)闅斿紑銆?br>渚嬶細21,80,443,1000:2000銆?

msgid "TCP Proxy Drop Ports"
msgstr "TCP 杞彂灞忚斀绔彛"

msgid "UDP Proxy Drop Ports"
msgstr "UDP 杞彂灞忚斀绔彛"

msgid "TCP Redir Ports"
msgstr "TCP 杞彂绔彛"

msgid "UDP Redir Ports"
msgstr "UDP 杞彂绔彛"

msgid "No patterns are used"
msgstr "涓嶄娇鐢?

msgid "All"
msgstr "鎵€鏈?

msgid "Common Use"
msgstr "甯哥敤鐨?

msgid "Only Web"
msgstr "浠呯綉椤?

msgid "Default"
msgstr "榛樿"

msgid "Close"
msgstr "鍏抽棴"

msgid "Hijacking ICMP (PING)"
msgstr "鍔寔ICMP (PING)"

msgid "Hijacking ICMPv6 (IPv6 PING)"
msgstr "鍔寔ICMPv6 (IPv6 PING)"

msgid "Sniffing"
msgstr "娴侀噺鍡呮帰"

msgid "TCP Proxy Way"
msgstr "TCP 浠ｇ悊鏂瑰紡"

msgid "Proxy Settings"
msgstr "浠ｇ悊閰嶇疆"

msgid "Auto Switch"
msgstr "鑷姩鍒囨崲"

msgid "How often to test"
msgstr "澶氫箙妫€娴嬩竴娆?

msgid "Timeout seconds"
msgstr "瓒呮椂绉掓暟"

msgid "Timeout retry num"
msgstr "瓒呮椂閲嶈瘯娆℃暟"

msgid "Main node"
msgstr "涓昏妭鐐?

msgid "List of backup nodes"
msgstr "澶囩敤鑺傜偣鐨勫垪琛?

msgid "Restore Switch"
msgstr "鎭㈠鍒囨崲"

msgid "When detects main node is available, switch back to the main node."
msgstr "褰撴娴嬪埌涓昏妭鐐瑰彲鐢ㄦ椂锛屽垏鎹㈠洖涓昏妭鐐广€?

msgid "If the main node is V2ray/Xray shunt"
msgstr "濡傛灉涓昏妭鐐规槸 V2ray/Xray 鍒嗘祦"

msgid "Switch it"
msgstr "鍒囨帀瀹?

msgid "Applying to the default node"
msgstr "搴旂敤浜庨粯璁よ妭鐐?

msgid "Applying to the preproxy node"
msgstr "搴旂敤浜庡墠缃唬鐞嗚妭鐐?

msgid "Add nodes to the standby node list by keywords"
msgstr "閫氳繃鍏抽敭瀛楁坊鍔犺妭鐐瑰埌澶囩敤鑺傜偣鍒楄〃"

msgid "Delete nodes in the standby node list by keywords"
msgstr "閫氳繃鍏抽敭瀛楀垹闄ゅ鐢ㄨ妭鐐瑰垪琛ㄧ殑鑺傜偣"

msgid "Please enter the node keyword, pay attention to distinguish between spaces, uppercase and lowercase."
msgstr "璇疯緭鍏ヨ妭鐐瑰叧閿瓧锛屾敞鎰忓尯鍒嗙┖鏍笺€佸ぇ鍐欏拰灏忓啓銆?

msgid "Configure this node with 127.0.0.1: this port"
msgstr "浣跨敤 127.0.0.1 鍜屾绔彛閰嶇疆鑺傜偣"

msgid "Enable Load Balancing"
msgstr "寮€鍚礋杞藉潎琛?

msgid "Console Login Auth"
msgstr "鎺у埗鍙扮櫥褰曡璇?

msgid "Console Username"
msgstr "鎺у埗鍙拌处鍙?

msgid "Console Password"
msgstr "鎺у埗鍙板瘑鐮?

msgid "Console Port"
msgstr "鎺у埗鍙扮鍙?

msgid "In the browser input routing IP plus port access, such as:***********:1188"
msgstr "鍦ㄦ祻瑙堝櫒杈撳叆璺敱IP鍔犵鍙ｈ闂紝濡傦細***********:1188"

msgid "Haproxy Port"
msgstr "璐熻浇鍧囪　绔彛"

msgid "Health Check Type"
msgstr "鍋ュ悍妫€鏌ョ被鍨?

msgid "Inner implement"
msgstr "鍐呯疆瀹炵幇"

msgid "Health Check Inter"
msgstr "鍋ュ悍妫€鏌ヨ妭鐐归棿闅旀椂闂?

msgid "When the URL test is used, the load balancing node will be converted into a Socks node. when node list set customizing, must be a Socks node, otherwise the health check will be invalid."
msgstr "褰撲娇鐢?URL 娴嬭瘯鏃讹紝璐熻浇鍧囪　鑺傜偣灏嗚浆鎹㈡垚 Socks 鑺傜偣銆備笅闈㈢殑鑺傜偣鍒楄〃鑷畾涔夋椂蹇呴』涓?Socks 鑺傜偣锛屽惁鍒欏仴搴锋鏌ュ皢鏃犳晥銆?

msgid "Add a node, Export Of Multi WAN Only support Multi Wan. Load specific gravity range 1-256. Multiple primary servers can be load balanced, standby will only be enabled when the primary server is offline! Multiple groups can be set, Haproxy port same one for each group."
msgstr "娣诲姞鑺傜偣锛屾寚瀹氬嚭鍙ｅ姛鑳芥槸涓哄 WAN 鐢ㄦ埛鍑嗗鐨勩€傝礋杞芥瘮閲嶈寖鍥?1-256銆傚涓富鏈嶅姟鍣ㄥ彲浠ヨ礋杞藉潎琛★紝澶囩敤鍙湁鍦ㄤ富鏈嶅姟鍣ㄧ绾挎椂鎵嶄細鍚敤锛佸彲浠ヨ缃涓粍锛岃礋杞藉潎琛＄鍙ｇ浉鍚屽垯涓轰竴缁勩€?

msgid "Note that the node configuration parameters for load balancing must be consistent when use TCP health check type, otherwise it cannot be used normally!"
msgstr "娉ㄦ剰锛屽綋浣跨敤 TCP 鍋ュ悍妫€鏌ユ椂璐熻浇鍧囪　鐨勮妭鐐归厤缃弬鏁板繀椤讳竴鑷达紝鍚﹀垯鏃犳硶姝ｅ父浣跨敤锛?

msgid "Node"
msgstr "鑺傜偣"

msgid "Node Address"
msgstr "鑺傜偣鍦板潃"

msgid "Node Port"
msgstr "鑺傜偣绔彛"

msgid "Node Weight"
msgstr "璐熻浇姣旈噸"

msgid "Export Of Multi WAN"
msgstr "澶?WAN 鎸囧畾鍑哄彛"

msgid "Main"
msgstr "涓昏"

msgid "Mode"
msgstr "妯″紡"

msgid "Primary"
msgstr "涓昏"

msgid "Standby"
msgstr "澶囩敤"

msgid "Check update"
msgstr "妫€鏌ユ洿鏂?

msgid "Force update"
msgstr "寮哄埗鏇存柊"

msgid "Manually update"
msgstr "鎵嬪姩鏇存柊"

msgid "The latest version: %s, currently does not support automatic update, if you need to update, please compile or download the ipk and then manually install."
msgstr "鏈€鏂扮増鏈細%s锛岀洰鍓嶆殏涓嶆敮鎸佽嚜鍔ㄦ洿鏂帮紝濡傞渶鏇存柊锛岃鑷缂栬瘧鎴栦笅杞?ipk 鐒跺悗鎵嬪姩瀹夎銆?

msgid "Enable custom URL"
msgstr "鍚敤鑷畾涔夎鍒欏湴鍧€"

msgid "GFW domains(gfwlist) Update URL"
msgstr "闃茬伀澧欏煙鍚嶅垪琛?gfwlist)鏇存柊URL"

msgid "China IPs(chnroute) Update URL"
msgstr "涓浗IP娈?chnroute)鏇存柊URL"

msgid "China IPv6s(chnroute6) Update URL"
msgstr "涓浗IPv6娈?chnroute6)鏇存柊URL"

msgid "China List(Chnlist) Update URL"
msgstr "涓浗鍩熷悕鍒楄〃(Chnlist)鏇存柊URL"

msgid "Rule status"
msgstr "瑙勫垯鐗堟湰"

msgid "Enable auto update rules"
msgstr "寮€鍚嚜鍔ㄦ洿鏂拌鍒?

msgid "Update Time(every day)"
msgstr "鏇存柊鏃堕棿(姣忓ぉ)"

msgid "Update Interval(hour)"
msgstr "鏇存柊闂撮殧(灏忔椂)"

msgid "Update Mode"
msgstr "鏇存柊妯″紡"

msgid "Loop Mode"
msgstr "寰幆"

msgid "Every day"
msgstr "姣忓ぉ"

msgid "Every Monday"
msgstr "姣忓懆涓€"

msgid "Every Tuesday"
msgstr "姣忓懆浜?

msgid "Every Wednesday"
msgstr "姣忓懆涓?

msgid "Every Thursday"
msgstr "姣忓懆鍥?

msgid "Every Friday"
msgstr "姣忓懆浜?

msgid "Every Saturday"
msgstr "姣忓懆鍏?

msgid "Every Sunday"
msgstr "姣忓懆鏃?

msgid "hour"
msgstr "灏忔椂"

msgid "Hour"
msgstr "灏忔椂"

msgid "GeoIP Update URL"
msgstr "GeoIP 鏇存柊URL"

msgid "Geosite Update URL"
msgstr "Geosite 鏇存柊URL"

msgid "Location of Geo rule files"
msgstr "Geo 瑙勫垯鏂囦欢鐩綍"

msgid "This variable specifies a directory where geoip.dat and geosite.dat files are."
msgstr "姝ゅ彉閲忔寚瀹?geoip.dat 鍜?geosite.dat 鏂囦欢鎵€鍦ㄧ殑鐩綍銆?

msgid "Enable Geo Data Parsing"
msgstr "寮€鍚?Geo 鏁版嵁瑙ｆ瀽"

msgid "Analyzes and preloads GeoIP/Geosite data to enhance the shunt performance of Sing-box/Xray."
msgstr "鍒嗘瀽鍜岄鍔犺浇 GeoIP/Geosite 鏁版嵁锛屼互澧炲己 Sing-box/Xray 鐨勫垎娴佹晥鏋溿€?

msgid "Once enabled, the rule list can support GeoIP/Geosite rules."
msgstr "鍚敤鍚庯紝瑙勫垯鍒楄〃鍙互鏀寔 GeoIP/Geosite 瑙勫垯銆?

msgid "Note: Increases resource usage; Geosite analysis is only supported in ChinaDNS-NG and SmartDNS modes."
msgstr "娉細浼氬鍔犱竴浜涚郴缁熻祫婧愮殑寮€閿€锛屼粎鍦?ChinaDNS-NG 鍜?SmartDNS 妯″紡涓嬫敮鎸佸垎鏋?Geosite 銆?

msgid "Shunt Rule"
msgstr "鍒嗘祦瑙勫垯"

msgid "Please note attention to the priority, the higher the order, the higher the priority."
msgstr "璇锋敞鎰忎紭鍏堢骇闂锛屾帓搴忚秺涓婇潰浼樺厛绾ц秺楂樸€?

msgid "Update..."
msgstr "鏇存柊涓?

msgid "It is the latest version"
msgstr "宸叉槸鏈€鏂扮増鏈?

msgid "Update successful"
msgstr "鏇存柊鎴愬姛"

msgid "Click to update"
msgstr "鐐瑰嚮鏇存柊"

msgid "Updating..."
msgstr "鏇存柊涓?

msgid "Retry"
msgstr "閲嶈瘯"

msgid "Unexpected error"
msgstr "鎰忓閿欒"

msgid "Updating, are you sure to close?"
msgstr "姝ｅ湪鏇存柊锛屼綘纭瑕佸叧闂悧锛?

msgid "Downloading..."
msgstr "涓嬭浇涓?

msgid "Unpacking..."
msgstr "瑙ｅ帇涓?

msgid "Moving..."
msgstr "绉诲姩涓?

msgid "App Update"
msgstr "缁勪欢鏇存柊"

msgid "Please confirm that your firmware supports FPU."
msgstr "璇风‘璁や綘鐨勫浐浠舵敮鎸?FPU銆?

msgid "if you want to run from memory, change the path, /tmp beginning then save the application and update it manually."
msgstr "濡傛灉浣犲笇鏈涗粠鍐呭瓨涓繍琛岋紝璇锋洿鏀硅矾寰勶紝/tmp 寮€澶达紝鐒跺悗淇濆瓨搴旂敤鍚庯紝鍐嶆墜鍔ㄦ洿鏂般€?

msgid "Make sure there is enough space to install %s"
msgstr "纭繚鏈夎冻澶熺殑绌洪棿瀹夎 %s"

msgid "App Path"
msgstr "绋嬪簭璺緞"

msgid "%s App Path"
msgstr "%s 绋嬪簭璺緞"

msgid "%s Client App Path"
msgstr "%s 瀹㈡埛绔▼搴忚矾寰?

msgid "alternate API URL for version checking"
msgstr "鐢ㄤ簬鐗堟湰妫€鏌ョ殑 API URL"

msgid "Node Subscribe"
msgstr "鑺傜偣璁㈤槄"

msgid "Subscribe Remark"
msgstr "璁㈤槄澶囨敞锛堟満鍦猴級"

msgid "Subscribe Info"
msgstr "璁㈤槄淇℃伅"

msgid "Subscribe URL"
msgstr "璁㈤槄缃戝潃"

msgid "Subscribe URL Access Method"
msgstr "璁㈤槄缃戝潃璁块棶鏂瑰紡"

msgid "Please input the subscription url first, save and submit before manual subscription."
msgstr "璇疯緭鍏ヨ闃呯綉鍧€淇濆瓨搴旂敤鍚庡啀鎵嬪姩璁㈤槄銆?

msgid "Subscribe via proxy"
msgstr "閫氳繃浠ｇ悊璁㈤槄"

msgid "Enable auto update subscribe"
msgstr "寮€鍚嚜鍔ㄦ洿鏂拌闃?

msgid "Manual subscription"
msgstr "鎵嬪姩璁㈤槄"

msgid "Delete All Subscribe Node"
msgstr "鍒犻櫎鎵€鏈夎闃呰妭鐐?

msgid "Delete the subscribed node"
msgstr "鍒犻櫎宸茶闃呯殑鑺傜偣"

msgid "Are you sure you want to delete all subscribed nodes?"
msgstr "鎮ㄧ‘瀹氳鍒犻櫎鎵€鏈夊凡璁㈤槄鐨勮妭鐐瑰悧锛?

msgid "Manual subscription All"
msgstr "鎵嬪姩璁㈤槄鍏ㄩ儴"

msgid "This remark already exists, please change a new remark."
msgstr "姝ゅ娉ㄥ凡瀛樺湪锛岃鏀逛竴涓柊鐨勫娉ㄣ€?

msgid "Filter keyword Mode"
msgstr "杩囨护鍏抽敭瀛楁ā寮?

msgid "Discard List"
msgstr "涓㈠純鍒楄〃"

msgid "Keep List"
msgstr "淇濈暀鍒楄〃"

msgid "Discard List,But Keep List First"
msgstr "涓㈠純鍒楄〃锛屼絾淇濈暀鍒楄〃浼樺厛"

msgid "Keep List,But Discard List First"
msgstr "淇濈暀鍒楄〃锛屼絾涓㈠純鍒楄〃浼樺厛"

msgid "Use global config"
msgstr "浣跨敤鍏ㄥ眬閰嶇疆"

msgid "User-Agent"
msgstr "鐢ㄦ埛浠ｇ悊(User-Agent)"

msgid "Add"
msgstr "娣诲姞"

msgid "ACLs"
msgstr "璁块棶鎺у埗"

msgid "ACLs is a tools which used to designate specific IP proxy mode."
msgstr "璁块棶鎺у埗鍒楄〃鏄敤浜庢寚瀹氱壒娈?IP 浠ｇ悊妯″紡鐨勫伐鍏枫€?

msgid "Example:"
msgstr "渚嬶細"

msgid "IP range"
msgstr "IP 鑼冨洿"

msgid "Source Interface"
msgstr "婧愭帴鍙?

msgid "Use Interface With ACLs"
msgstr "浣跨敤鎺ュ彛鎺у埗"

msgid "Remarks"
msgstr "澶囨敞"

msgid "Direct List"
msgstr "鐩磋繛鍒楄〃"

msgid "Proxy List"
msgstr "浠ｇ悊鍒楄〃"

msgid "Block List"
msgstr "灞忚斀鍒楄〃"

msgid "Lan IP List"
msgstr "灞€鍩熺綉 IP 鍒楄〃"

msgid "Route Hosts"
msgstr "璺敱 Hosts 鏂囦欢"

msgid "Join the direct hosts list of domain names will not proxy."
msgstr "鍔犲叆鐨勫煙鍚嶄笉璧颁唬鐞嗭紝瀵规墍鏈夋ā寮忔湁鏁堛€備笖浼樺厛绾ф渶楂樸€?

msgid "These had been joined ip addresses will not proxy. Please input the ip address or ip address segment,every line can input only one ip address. For example: ***********/24 or *********."
msgstr "鍔犲叆鐨?IP 娈典笉璧颁唬鐞嗭紝瀵规墍鏈夋ā寮忔湁鏁堛€備笖浼樺厛绾ф渶楂樸€傚彲杈撳叆 IP 鍦板潃鎴栧湴鍧€娈碉紝濡傦細***********/24 鎴?*********锛屾瘡涓湴鍧€娈典竴琛屻€?

msgid "These had been joined websites will use proxy. Please input the domain names of websites, every line can input only one website domain. For example: google.com."
msgstr "鍔犲叆鐨勫煙鍚嶅皢璧颁唬鐞嗐€傝緭鍏ョ綉绔欏煙鍚嶏紝濡傦細google.com锛屾瘡涓湴鍧€娈典竴琛屻€?

msgid "These had been joined ip addresses will use proxy. Please input the ip address or ip address segment, every line can input only one ip address. For example: *********/24 or *******."
msgstr "鍔犲叆鐨?IP 娈靛皢璧颁唬鐞嗐€傚彲杈撳叆 IP 鍦板潃鎴栧湴鍧€娈碉紝濡傦細*********/24 鎴?*******锛屾瘡涓湴鍧€娈典竴琛屻€?

msgid "These had been joined websites will be block. Please input the domain names of websites, every line can input only one website domain. For example: twitter.com."
msgstr "鍔犲叆鐨勫煙鍚嶅皢灞忚斀銆傝緭鍏ョ綉绔欏煙鍚嶏紝濡傦細twitter.com锛屾瘡涓湴鍧€娈典竴琛屻€?

msgid "The list is the IPv4 LAN IP list, which represents the direct connection IP of the LAN. If you need the LAN IP in the proxy list, please clear it from the list. Do not modify this list by default."
msgstr "鍒楄〃涓负 IPv4 鐨勫眬鍩熺綉 IP 鍒楄〃锛屼唬琛ㄥ眬鍩熺綉鐩磋繛 IP銆傚鏋滈渶瑕佷唬鐞嗗垪琛ㄤ腑鐨勫眬鍩熺綉 IP锛岃灏嗗叾鍦ㄨ鍒楄〃涓竻闄わ紝骞跺皢鍏舵坊鍔犲埌浠ｇ悊鍒楄〃涓€傞粯璁ゆ儏鍐典笅涓嶈淇敼杩欎釜鍒楄〃銆?

msgid "The list is the IPv6 LAN IP list, which represents the direct connection IP of the LAN. If you need the LAN IP in the proxy list, please clear it from the list. Do not modify this list by default."
msgstr "鍒楄〃涓负 IPv6 鐨勫眬鍩熺綉 IP 鍒楄〃锛屼唬琛ㄥ眬鍩熺綉鐩磋繛 IP銆傚鏋滈渶瑕佷唬鐞嗗垪琛ㄤ腑鐨勫眬鍩熺綉 IP锛岃灏嗗叾鍦ㄨ鍒楄〃涓竻闄わ紝骞跺皢鍏舵坊鍔犲埌浠ｇ悊鍒楄〃涓€傞粯璁ゆ儏鍐典笅涓嶈淇敼杩欎釜鍒楄〃銆?

msgid "Configure routing etc/hosts file, if you don't know what you are doing, please don't change the content."
msgstr "閰嶇疆璺敱 etc/hosts 鏂囦欢锛屽鏋滀綘涓嶇煡閬撹嚜宸卞湪鍋氫粈涔堬紝璇蜂笉瑕佹敼鍔ㄥ唴瀹广€?

msgid "These had been joined ip addresses will be block. Please input the ip address or ip address segment, every line can input only one ip address."
msgstr "鍔犲叆鐨?IP 娈靛皢灞忚斀銆傚彲杈撳叆 IP 鍦板潃鎴栧湴鍧€娈碉紝姣忎釜鍦板潃娈典竴琛屻€?

msgid "Inbound Tag"
msgstr "鍏ョ珯鏍囩"

msgid "Transparent proxy"
msgstr "閫忔槑浠ｇ悊"

msgid "Not valid domain name, please re-enter!"
msgstr "涓嶆槸鏈夋晥鍩熷悕锛岃閲嶆柊杈撳叆锛?

msgid "Not valid IP format, please re-enter!"
msgstr "涓嶆槸鏈夋晥 IP 鏍煎紡锛岃閲嶆柊杈撳叆锛?

msgid "Not valid IPv4 format, please re-enter!"
msgstr "涓嶆槸鏈夋晥 IPv4 鏍煎紡锛岃閲嶆柊杈撳叆锛?

msgid "Not valid IPv6 format, please re-enter!"
msgstr "涓嶆槸鏈夋晥 IPv6 鏍煎紡锛岃閲嶆柊杈撳叆锛?

msgid "Not true format, please re-enter!"
msgstr "涓嶆槸姝ｇ‘鐨勬牸寮忥紝璇烽噸鏂拌緭鍏ワ紒"

msgid "Plaintext: If this string matches any part of the targeting domain, this rule takes effet. Example: rule 'sina.com' matches targeting domain 'sina.com', 'sina.com.cn' and 'www.sina.com', but not 'sina.cn'."
msgstr "绾瓧绗︿覆: 褰撴瀛楃涓插尮閰嶇洰鏍囧煙鍚嶄腑浠绘剰閮ㄥ垎锛岃瑙勫垯鐢熸晥銆傛瘮濡?sina.com'鍙互鍖归厤'sina.com'銆?sina.com.cn'鍜?www.sina.com'锛屼絾涓嶅尮閰?sina.cn'銆?

msgid "Regular expression: Begining with 'regexp:', the rest is a regular expression. When the regexp matches targeting domain, this rule takes effect. Example: rule 'regexp:\\.goo.*\\.com$' matches 'www.google.com' and 'fonts.googleapis.com', but not 'google.com'."
msgstr "姝ｅ垯琛ㄨ揪寮? 鐢?regexp:'寮€濮嬶紝浣欎笅閮ㄥ垎鏄竴涓鍒欒〃杈惧紡銆傚綋姝ゆ鍒欒〃杈惧紡鍖归厤鐩爣鍩熷悕鏃讹紝璇ヨ鍒欑敓鏁堛€備緥濡?regexp:\\.goo.*\\.com$'鍖归厤'www.google.com'銆?fonts.googleapis.com'锛屼絾涓嶅尮閰?google.com'銆?

msgid "Subdomain (recommended): Begining with 'domain:' and the rest is a domain. When the targeting domain is exactly the value, or is a subdomain of the value, this rule takes effect. Example: rule 'domain:v2ray.com' matches 'www.v2ray.com', 'v2ray.com', but not 'xv2ray.com'."
msgstr "瀛愬煙鍚?(鎺ㄨ崘): 鐢?domain:'寮€濮嬶紝浣欎笅閮ㄥ垎鏄竴涓煙鍚嶃€傚綋姝ゅ煙鍚嶆槸鐩爣鍩熷悕鎴栧叾瀛愬煙鍚嶆椂锛岃瑙勫垯鐢熸晥銆備緥濡?domain:v2ray.com'鍖归厤'www.v2ray.com'銆?v2ray.com'锛屼絾涓嶅尮閰?xv2ray.com'銆?

msgid "Full domain: Begining with 'full:' and the rest is a domain. When the targeting domain is exactly the value, the rule takes effect. Example: rule 'domain:v2ray.com' matches 'v2ray.com', but not 'www.v2ray.com'."
msgstr "瀹屾暣鍖归厤: 鐢?full:'寮€濮嬶紝浣欎笅閮ㄥ垎鏄竴涓煙鍚嶃€傚綋姝ゅ煙鍚嶅畬鏁村尮閰嶇洰鏍囧煙鍚嶆椂锛岃瑙勫垯鐢熸晥銆備緥濡?full:v2ray.com'鍖归厤'v2ray.com'浣嗕笉鍖归厤'www.v2ray.com'銆?

msgid "Pre-defined domain list: Begining with 'geosite:' and the rest is a name, such as geosite:google or geosite:cn."
msgstr "棰勫畾涔夊煙鍚嶅垪琛細鐢?geosite:'寮€澶达紝浣欎笅閮ㄥ垎鏄竴涓悕绉帮紝濡俫eosite:google鎴栬€単eosite:cn銆?

msgid "Annotation: Begining with #"
msgstr "娉ㄩ噴: 鐢?# 寮€澶?

msgid "IP: such as '127.0.0.1'."
msgstr "IP: 褰㈠'127.0.0.1'銆?

msgid "CIDR: such as '*********/8'."
msgstr "CIDR: 褰㈠'10.0.0.0/8'."

msgid "GeoIP: such as 'geoip:cn'. It begins with geoip: (lower case) and followed by two letter of country code."
msgstr "GeoIP: 褰㈠'geoip:cn'锛屽繀椤讳互geoip:锛堝皬鍐欙級寮€澶达紝鍚庨潰璺熷弻瀛楃鍥藉浠ｇ爜锛屾敮鎸佸嚑涔庢墍鏈夊彲浠ヤ笂缃戠殑鍥藉銆?

msgid "Clear logs"
msgstr "娓呯┖鏃ュ織"

msgid "Only recommend to use with VLESS-TCP-XTLS-Vision."
msgstr "鍙帹鑽愪笌 VLESS-TCP-XTLS-Vision 鎼厤浣跨敤銆?

msgid "Password"
msgstr "瀵嗙爜"

msgid "IV Check"
msgstr "IV 妫€鏌?

msgid "UDP over TCP"
msgstr "TCP 灏佽 UDP"

msgid "Connection Timeout"
msgstr "杩炴帴瓒呮椂鏃堕棿"

msgid "Local Port"
msgstr "鏈湴绔彛"

msgid "Fast Open"
msgstr "蹇€熸墦寮€"

msgid "Need node support required"
msgstr "闇€瑕佽妭鐐规敮鎸?

msgid "plugin"
msgstr "鎻掍欢"

msgid "Supports custom SIP003 plugins, Make sure the plugin is installed."
msgstr "鏀寔鑷畾涔?SIP003 鎻掍欢锛岃纭繚鎻掍欢宸插畨瑁呫€?

msgid "opts"
msgstr "鎻掍欢閫夐」"

msgid "Protocol"
msgstr "鍗忚鍚嶇О"

msgid "Protocol_param"
msgstr "鍗忚鍙傛暟"

msgid "Obfs"
msgstr "娣锋穯"

msgid "Obfs_param"
msgstr "娣锋穯鍙傛暟"

msgid "Plugin Name"
msgstr "鎻掍欢鍚嶇О"

msgid "Plugin Arguments"
msgstr "鎻掍欢鍙傛暟"

msgid "Naiveproxy Protocol"
msgstr "Naiveproxy 鍗忚"

msgid "V2ray Protocol"
msgstr "V2ray 鍗忚"

msgid "User Level"
msgstr "鐢ㄦ埛绛夌骇(level)"

msgid "Transport"
msgstr "浼犺緭鏂瑰紡"

msgid "Public Key"
msgstr "鍏挜"

msgid "Private Key"
msgstr "绉侀挜"

msgid "Pre shared key"
msgstr "棰濆鐨勫绉板姞瀵嗗瘑閽?

msgid "Local Address"
msgstr "鏈湴鍦板潃"

msgid "Decimal numbers separated by \",\" or Base64-encoded strings."
msgstr "鐢ㄢ€?鈥濋殧寮€鐨勫崄杩涘埗鏁板瓧鎴?Base64 缂栫爜瀛楃涓层€?

msgid "Camouflage Domain"
msgstr "浼鍩熷悕"

msgid "Camouflage Type"
msgstr "浼绫诲瀷"

msgid "Transport Layer Encryption"
msgstr "浼犺緭灞傚姞瀵?

msgid "Whether or not transport layer encryption is enabled, \"none\" for unencrypted, \"tls\" for using TLS, \"xtls\" for using XTLS."
msgstr "鏄惁鍚叆浼犺緭灞傚姞瀵嗭紝鏀寔鐨勯€夐」鏈?\"none\" 琛ㄧず涓嶅姞瀵嗭紝\"tls\" 琛ㄧず浣跨敤 TLS锛孿"xtls\" 琛ㄧず浣跨敤 XTLS銆?

msgid "Original Trojan only supported 'tls', please choose 'tls'."
msgstr "鍘熺増Trojan鍙敮鎸?tls'锛岃閫夋嫨'tls'銆?

msgid "Transfer mode"
msgstr "浼犺緭妯″紡"

msgid "Do not send server name in ClientHello."
msgstr "涓嶈鍦?ClientHello 涓彂閫佹湇鍔″櫒鍚嶇О銆?

msgid "Domain"
msgstr "鍩熷悕"

msgid "allowInsecure"
msgstr "鍏佽涓嶅畨鍏ㄨ繛鎺?

msgid "Whether unsafe connections are allowed. When checked, Certificate validation will be skipped."
msgstr "鏄惁鍏佽涓嶅畨鍏ㄨ繛鎺ャ€傚綋鍕鹃€夋椂锛屽皢璺宠繃璇佷功楠岃瘉銆?

msgid "%s Node Use Type"
msgstr "%s 鑺傜偣浣跨敤绫诲瀷"

msgid "Set the TUIC proxy server ip address"
msgstr "鎸囧畾杩滅▼ TUIC 鏈嶅姟鍣?IP"

msgid "TUIC User Password For Connect Remote Server"
msgstr "鐢ㄤ簬杩滅▼ TUIC 鏈嶅姟鍣ㄨ繛鎺ョ殑瀵嗙爜"

msgid "TUIC UserName For Local Socks"
msgstr "鐢ㄤ簬鏈湴 Socks 鏈嶅姟鍣ㄨ繛鎺ョ殑鐢ㄦ埛鍚?

msgid "TUIC Password For Local Socks"
msgstr "鐢ㄤ簬鏈湴 Socks 鏈嶅姟鍣ㄨ繛鎺ョ殑瀵嗙爜"

msgid "UDP relay mode"
msgstr "UDP 涓户妯″紡"

msgid "Congestion control algorithm"
msgstr "鎷ュ鎺у埗绠楁硶"

msgid "Heartbeat interval(second)"
msgstr "淇濇椿蹇冭烦鍖呭彂閫侀棿闅旓紙鍗曚綅锛氱锛?

msgid "Timeout for establishing a connection to server(second)"
msgstr "杩炴帴瓒呮椂鏃堕棿锛堝崟浣嶏細绉掞級"

msgid "Garbage collection interval(second)"
msgstr "UDP 鏁版嵁鍖呯墖娈嬬墖娓呯悊闂撮殧锛堝崟浣嶏細绉掞級"

msgid "Garbage collection lifetime(second)"
msgstr "UDP 鏁版嵁鍖呮畫鐗囧湪鏈嶅姟鍣ㄧ殑淇濈暀鏃堕棿锛堝崟浣嶏細绉掞級"

msgid "Disable SNI"
msgstr "鍏抽棴 SNI 鏈嶅姟鍣ㄥ悕绉版寚绀?

msgid "Enable 0-RTT QUIC handshake"
msgstr "瀹㈡埛绔惎鐢?0-RTT QUIC 杩炴帴鎻℃墜"

msgid "TUIC send window"
msgstr "鍙戦€佺獥鍙ｏ紙鏃犻渶纭鍗冲彲鍙戦€佺殑鏈€澶у瓧鑺傛暟锛氶粯璁?Mb*2锛?

msgid "TUIC receive window"
msgstr "鎺ユ敹绐楀彛锛堟棤闇€纭鍗冲彲鎺ユ敹鐨勬渶澶у瓧鑺傛暟锛氶粯璁?Mb锛?

msgid "TUIC Maximum packet size the socks5 server can receive from external, in bytes"
msgstr "TUIC socks5 鏈嶅姟鍣ㄥ彲浠ヤ粠澶栭儴鎺ユ敹鐨勬渶澶ф暟鎹寘澶у皬锛堜互瀛楄妭涓哄崟浣嶏級"

msgid "Set if the listening socket should be dual-stack"
msgstr "璁剧疆鐩戝惉濂楁帴瀛椾负鍙屾爤"

msgid "<br />none: default, no masquerade, data sent is packets with no characteristics.<br />srtp: disguised as an SRTP packet, it will be recognized as video call data (such as FaceTime).<br />utp: packets disguised as uTP will be recognized as bittorrent downloaded data.<br />wechat-video: packets disguised as WeChat video calls.<br />dtls: disguised as DTLS 1.2 packet.<br />wireguard: disguised as a WireGuard packet. (not really WireGuard protocol)<br />dns: Disguising traffic as DNS requests."
msgstr "<br />none锛氶粯璁ゅ€硷紝涓嶈繘琛屼吉瑁咃紝鍙戦€佺殑鏁版嵁鏄病鏈夌壒寰佺殑鏁版嵁鍖呫€?br />srtp锛氫吉瑁呮垚 SRTP 鏁版嵁鍖咃紝浼氳璇嗗埆涓鸿棰戦€氳瘽鏁版嵁锛堝 FaceTime锛夈€?br />utp锛氫吉瑁呮垚 uTP 鏁版嵁鍖咃紝浼氳璇嗗埆涓?BT 涓嬭浇鏁版嵁銆?br />wechat-video锛氫吉瑁呮垚寰俊瑙嗛閫氳瘽鐨勬暟鎹寘銆?br />dtls锛氫吉瑁呮垚 DTLS 1.2 鏁版嵁鍖呫€?br />wireguard锛氫吉瑁呮垚 WireGuard 鏁版嵁鍖呫€?骞朵笉鏄湡姝ｇ殑 WireGuard 鍗忚)<br />dns锛氭妸娴侀噺浼鎴?DNS 璇锋眰銆?

msgid "Use it together with the DNS disguised type. You can fill in any domain."
msgstr "閰嶅悎浼绫诲瀷 DNS 浣跨敤锛屽彲闅忎究濉竴涓煙鍚嶃€?

msgid "A legal file path. This file must not exist before running."
msgstr "涓€涓悎娉曠殑鏂囦欢璺緞銆傚湪杩愯涔嬪墠锛岃繖涓枃浠跺繀椤讳笉瀛樺湪銆?

msgid "Auth"
msgstr "韬唤璁よ瘉"

msgid "Socks for authentication"
msgstr "Socks 璁よ瘉鏂瑰紡"

msgid "Socks protocol authentication, support anonymous and password."
msgstr "Socks 鍗忚鐨勮璇佹柟寮忥紝鏀寔鍖垮悕鏂瑰紡鍜岃处鍙峰瘑鐮佹柟寮忋€?

msgid "anonymous"
msgstr "鍖垮悕"

msgid "User Password"
msgstr "璐﹀彿瀵嗙爜"

msgid "Username and Password must be used together!"
msgstr "璐﹀彿鍜屽瘑鐮佸繀椤诲悓鏃朵娇鐢紒"

msgid "Node Number"
msgstr "鑺傜偣鏁伴噺"

msgid "You can only set up a maximum of %s nodes for the time being, Used for access control."
msgstr "鐩墠鏈€澶氬彧鑳借缃?%s 涓妭鐐癸紝鐢ㄤ簬缁欒闂帶鍒朵娇鐢ㄣ€?

msgid "Firewall tools"
msgstr "闃茬伀澧欏伐鍏?

msgid "IPv6 TProxy"
msgstr "IPv6 閫忔槑浠ｇ悊(TProxy)"

msgid "Experimental feature. Make sure that your node supports IPv6."
msgstr "瀹為獙鐗规€э紝璇风‘淇濅綘鐨勮妭鐐规敮鎸両Pv6"

msgid "Status info"
msgstr "鐘舵€佷俊鎭?

msgid "Big icon"
msgstr "澶у浘鏍?

msgid "Show node check"
msgstr "鏄剧ず鑺傜偣妫€娴?

msgid "Show Show IP111"
msgstr "鏄剧ず IP111"

msgid "Destination protocol"
msgstr "鐩爣鍗忚"

msgid "Destination address"
msgstr "鐩爣鍦板潃"

msgid "Destination port"
msgstr "鐩爣绔彛"

msgid "Whether to receive PROXY protocol, when this node want to be fallback or forwarded by proxy, it must be enable, otherwise it cannot be used."
msgstr "鏄惁鎺ユ敹 PROXY protocol锛屽綋璇ヨ妭鐐硅琚洖钀芥垨琚唬鐞嗚浆鍙戞椂锛屽繀椤诲惎鐢紝鍚﹀垯涓嶈兘浣跨敤銆?

msgid "outbound node"
msgstr "鍑虹珯鑺傜偣"

msgid "Custom Socks"
msgstr "鑷畾涔?Socks"

msgid "Custom HTTP"
msgstr "鑷畾涔?HTTP"

msgid "Custom Interface"
msgstr "鑷畾涔夋帴鍙?

msgid "Interface"
msgstr "鎺ュ彛"

msgid "Bind Local"
msgstr "鏈満鐩戝惉"

msgid "When selected, it can only be accessed localhost."
msgstr "褰撳嬀閫夋椂锛屽彧鑳芥湰鏈鸿闂€?

msgid "Accept LAN Access"
msgstr "鎺ュ彈灞€鍩熺綉璁块棶"

msgid "When selected, it can accessed lan , this will not be safe!"
msgstr "褰撳嬀閫夋椂锛屽彲浠ョ洿鎺ヨ闂眬鍩熺綉锛岃繖灏嗕笉瀹夊叏锛侊紙闈炵壒娈婃儏鍐典笉寤鸿寮€鍚級"

msgid "Enable Remote"
msgstr "鍚敤杞彂"

msgid "You can forward to Nginx/Caddy/V2ray/Xray WebSocket and more."
msgstr "鎮ㄥ彲浠ヨ浆鍙戝埌 Nginx/Caddy/V2ray/Xray WebSocket 绛夈€?

msgid "Remote Address"
msgstr "杩滅▼鍦板潃"

msgid "Remote Port"
msgstr "杩滅▼绔彛"

msgid "as:"
msgstr "濡傦細"

msgid "Public key absolute path"
msgstr "鍏挜鏂囦欢缁濆璺緞"

msgid "Private key absolute path"
msgstr "绉侀挜鏂囦欢缁濆璺緞"

msgid "Can't find this file!"
msgstr "鎵句笉鍒拌繖涓枃浠讹紒"

msgid "Public key and Private key path can not be empty!"
msgstr "鍏挜鍜岀閽ユ枃浠惰矾寰勪笉鑳戒负绌猴紒"

msgid "Server-Side"
msgstr "鏈嶅姟鍣ㄧ"

msgid "Server Config"
msgstr "鏈嶅姟鍣ㄩ厤缃?

msgid "Users Manager"
msgstr "鐢ㄦ埛绠＄悊"

msgid "Logs"
msgstr "鏃ュ織"

msgid "Log"
msgstr "鏃ュ織"

msgid "%s Node Log"
msgstr "%s 鑺傜偣鏃ュ織"

msgid "Log Level"
msgstr "鏃ュ織绛夌骇"

msgid "Advanced log feature"
msgstr "楂樼骇鏃ュ織鍔熻兘"

msgid "For professionals only."
msgstr "浠呴檺涓撲笟浜哄＋浣跨敤銆?

msgid "Persist log file directory"
msgstr "鎸佷箙鎬ф棩蹇楁枃浠剁洰褰?

msgid "The path to the directory used to store persist log files, the \"/\" at the end can be omitted. Leave it blank to disable this feature."
msgstr "鐢ㄦ潵瀛樺偍鎸佷箙鎬ф棩蹇楁枃浠剁殑鐩綍璺緞锛屾湯灏剧殑 鈥?鈥?鍙互鐪佺暐銆傜暀绌轰互绂佺敤姝ゅ姛鑳姐€?

msgid "Logging to system log"
msgstr "璁板綍鍒扮郴缁熸棩蹇?

msgid "Logging to the system log for more advanced functions. For example, send logs to a dedicated log server."
msgstr "灏嗘棩蹇楄褰曞埌绯荤粺鏃ュ織锛屼互瀹炵幇鏇村姞楂樼骇鐨勫姛鑳姐€備緥濡傦紝鎶婃棩蹇楀彂閫佸埌涓撻棬鐨勬棩蹇楁湇鍔″櫒銆?

msgid "Log Event Filter"
msgstr "鏃ュ織浜嬩欢杩囨护鍣?

msgid "Support regular expression."
msgstr "鏀寔姝ｅ垯琛ㄨ揪寮忋€?

msgid "Shell Command"
msgstr "Shell 鍛戒护"

msgid "Shell command to execute, replace log content with %s."
msgstr "瑕佹墽琛岀殑 Shell 鍛戒护锛岀敤 %s 浠ｆ浛鏃ュ織鍐呭銆?

msgid "Not enabled log"
msgstr "鏈惎鐢ㄦ棩蹇?

msgid "It is recommended to disable logging during regular use to reduce system overhead."
msgstr "姝ｅ父浣跨敤鏃跺缓璁叧闂棩蹇楋紝浠ュ噺灏戠郴缁熷紑閿€銆?

msgid "UDP Forward"
msgstr "UDP 杞彂"

msgid "DNS Settings"
msgstr "DNS 璁剧疆"

msgid "Null"
msgstr "鏃?

msgid "You did not fill in the %s path. Please save and apply then update manually."
msgstr "鎮ㄦ病鏈夊～鍐?%s 璺緞銆傝淇濆瓨搴旂敤鍚庡啀鎵嬪姩鏇存柊銆?

msgid "Not installed %s, Can't unzip!"
msgstr "鏈畨瑁?%s锛屾棤娉曡В鍘嬶紒"

msgid "Can't determine ARCH, or ARCH not supported."
msgstr "鏃犳硶纭ARCH鏋舵瀯锛屾垨鏄笉鏀寔銆?

msgid "Get remote version info failed."
msgstr "鑾峰彇杩滅▼鐗堟湰淇℃伅澶辫触銆?

msgid "New version found, but failed to get new version download url."
msgstr "鍙戠幇鏂扮増鏈紝浣嗘湭鑳借幏寰楁柊鐗堟湰鐨勪笅杞藉湴鍧€銆?

msgid "Download url is required."
msgstr "璇锋寚瀹氫笅杞藉湴鍧€銆?

msgid "File download failed or timed out: %s"
msgstr "鏂囦欢涓嬭浇澶辫触鎴栬秴鏃讹細%s"

msgid "File path required."
msgstr "璇锋寚瀹氭枃浠惰矾寰勩€?

msgid "%s not enough space."
msgstr "%s 绌洪棿涓嶈冻銆?

msgid "Can't find client in file: %s"
msgstr "鏃犳硶鍦ㄦ枃浠朵腑鎵惧埌瀹㈡埛绔細%s"

msgid "Client file is required."
msgstr "璇锋寚瀹氬鎴风鏂囦欢銆?

msgid "The client file is not suitable for current device."
msgstr "瀹㈡埛绔枃浠朵笉閫傚悎褰撳墠璁惧銆?

msgid "Can't move new file to path: %s"
msgstr "鏃犳硶绉诲姩鏂版枃浠跺埌锛?s"

msgid "An XHttpObject in JSON format, used for sharing."
msgstr "JSON 鏍煎紡鐨?XHttpObject锛岀敤鏉ュ疄鐜板垎浜€?

msgid "Enable Mux.Cool"
msgstr "鍚敤 Mux.Cool"

msgid "Mux concurrency"
msgstr "鏈€澶у苟鍙戣繛鎺ユ暟"

msgid "XUDP Mux concurrency"
msgstr "XUDP 鏈€澶у苟鍙戣繛鎺ユ暟"

msgid "Padding"
msgstr "濉厖"

msgid "Enable early data"
msgstr "鍚敤鍓嶇疆鏁版嵁"

msgid "Early data length"
msgstr "鍓嶇疆鏁版嵁鏈€澶ч暱搴?

msgid "Early data header name"
msgstr "鍓嶇疆鏁版嵁 HTTP 澶村悕"

msgid "Recommended value: Sec-WebSocket-Protocol"
msgstr "鎺ㄨ崘鍊硷細Sec-WebSocket-Protocol"

msgid "Health check"
msgstr "鍋ュ悍妫€鏌?

msgid "Health check timeout"
msgstr "妫€鏌ヨ秴鏃舵椂闂?

msgid "Permit without stream"
msgstr "鏃犲瓙杩炴帴鏃剁殑鍋ュ悍妫€鏌?

msgid "Initial Windows Size"
msgstr "鍒濆绐楀彛澶у皬"

msgid "Excluded Domains"
msgstr "鎺掗櫎鍩熷悕"

msgid "If the traffic sniffing result is in this list, the destination address will not be overridden."
msgstr "濡傛灉娴侀噺鍡呮帰缁撴灉鍦ㄦ鍒楄〃涓紝鍒欎笉浼氳鐩栫洰鏍囧湴鍧€銆?

msgid "Buffer Size"
msgstr "缂撳啿鍖哄ぇ灏?

msgid "Buffer size for every connection (kB)"
msgstr "姣忎竴涓繛鎺ョ殑缂撳啿鍖哄ぇ灏忥紙kB锛?

msgid "Handshake Timeout"
msgstr "鎻℃墜瓒呮椂 "

msgid "Idle Timeout"
msgstr "绌洪棽瓒呮椂 "

msgid "Hop Interval"
msgstr "绔彛璺宠穬鏃堕棿 "

msgid "HeartbeatPeriod(second)"
msgstr "蹇冭烦鍛ㄦ湡锛堝崟浣嶏細绉掞級"

msgid "Override the connection destination address"
msgstr "瑕嗙洊杩炴帴鐩爣鍦板潃"

msgid "Handshake Server"
msgstr "鎻℃墜鏈嶅姟鍣?

msgid "Handshake Server Port"
msgstr "鎻℃墜鏈嶅姟鍣ㄧ鍙?

msgid "Override the connection destination address with the sniffed domain.<br />Otherwise use sniffed domain for routing only.<br />If using shunt nodes, configure the domain shunt rules correctly."
msgstr "鐢ㄦ帰娴嬪嚭鐨勫煙鍚嶈鐩栬繛鎺ョ洰鏍囧湴鍧€銆?br />鍚﹀垯浠呭皢鎺㈡祴寰楀埌鐨勫煙鍚嶇敤浜庤矾鐢便€?br />濡備娇鐢ㄥ垎娴佽妭鐐癸紝璇锋纭缃煙鍚嶅垎娴佽鍒欍€?

msgid "Override the connection destination address with the sniffed domain.<br />When enabled, traffic will match only by domain, ignoring IP rules.<br />If using shunt nodes, configure the domain shunt rules correctly."
msgstr "鐢ㄦ帰娴嬪嚭鐨勫煙鍚嶈鐩栬繛鎺ョ洰鏍囧湴鍧€銆?br />鍚敤鍚庝粎浣跨敤鍩熷悕杩涜娴侀噺鍖归厤锛屽皢蹇界暐IP瑙勫垯銆?br />濡備娇鐢ㄥ垎娴佽妭鐐癸紝璇锋纭缃煙鍚嶅垎娴佽鍒欍€?

msgid "Protocol parameter. Will waste traffic randomly if enabled."
msgstr "鍗忚鍙傛暟銆傚鏋滃惎鐢ㄤ細闅忔満娴垂娴侀噺銆?

msgid "Protocol parameter. Enable length block encryption."
msgstr "鍗忚鍙傛暟銆傚惎鐢ㄩ暱搴﹀潡鍔犲瘑銆?

msgid "ECH Config"
msgstr "ECH 瀵嗛挜"

msgid "ECH Key"
msgstr "ECH 閰嶇疆"

msgid "PQ signature schemes"
msgstr "鍚庨噺瀛愬绛夎瘉涔︾鍚嶆柟妗?

msgid "Disable adaptive sizing of TLS records"
msgstr "绂佺敤 TLS 璁板綍鐨勮嚜閫傚簲澶у皬璋冩暣"

msgid "Enable Multipath TCP, need to be enabled in both server and client configuration."
msgstr "鍚敤 Multipath TCP锛岄渶鍦ㄦ湇鍔＄鍜屽鎴风閰嶇疆涓悓鏃跺惎鐢ㄣ€?

msgid "Fragment"
msgstr "鍒嗙墖"

msgid "TCP fragments, which can deceive the censorship system in some cases, such as bypassing SNI blacklists."
msgstr "TCP 鍒嗙墖锛屽湪鏌愪簺鎯呭喌涓嬪彲浠ユ楠楀鏌ョ郴缁燂紝姣斿缁曡繃 SNI 榛戝悕鍗曘€?

msgid "Fragment Packets"
msgstr "鍒嗙墖鏂瑰紡"

msgid "\"1-3\" is for segmentation at TCP layer, applying to the beginning 1 to 3 data writes by the client. \"tlshello\" is for TLS client hello packet fragmentation."
msgstr "\"1-3\" 鏄?TCP 鐨勬祦鍒囩墖锛屽簲鐢ㄤ簬瀹㈡埛绔 1 鑷崇 3 娆″啓鏁版嵁銆俓"tlshello\" 鏄?TLS 鎻℃墜鍖呭垏鐗囥€?

msgid "Fragment Length"
msgstr "鍒嗙墖鍖呴暱"

msgid "Fragmented packet length (byte)"
msgstr "鍒嗙墖鍖呴暱 (byte)"

msgid "Fragment Interval"
msgstr "鍒嗙墖闂撮殧"

msgid "Fragmentation interval (ms)"
msgstr "鍒嗙墖闂撮殧锛坢s锛?

msgid "Noise"
msgstr "鍣０"

msgid "UDP noise, Under some circumstances it can bypass some UDP based protocol restrictions."
msgstr "UDP 鍣０锛屽湪鏌愪簺鎯呭喌涓嬪彲浠ョ粫杩囦竴浜涢拡瀵?UDP 鍗忚鐨勯檺鍒躲€?

msgid "To send noise packets, select \"Noise\" in Xray Settings."
msgstr "鍦?Xray 璁剧疆涓嬀閫?鈥滃櫔澹扳€?浠ュ彂閫佸櫔澹板寘銆?

msgid "Xray Noise Packets"
msgstr "Xray 鍣０鏁版嵁鍖?

msgid "Packet"
msgstr "鏁版嵁鍖?

msgid "Delay (ms)"
msgstr "寤惰繜锛坢s锛?

msgid "If is domain name, The requested domain name will be resolved to IP before connect."
msgstr "濡傛灉鏄煙鍚嶏紝鍩熷悕灏嗗湪璇锋眰鍙戝嚭涔嬪墠瑙ｆ瀽涓?IP銆?

msgid "Chain Proxy"
msgstr "閾惧紡浠ｇ悊"

msgid "Landing Node"
msgstr "钀藉湴鑺傜偣"

msgid "Only support a layer of proxy."
msgstr "浠呮敮鎸佷竴灞備唬鐞嗐€?

msgid "Only work with using the %s node."
msgstr "涓庝娇鐢?%s 鑺傜偣鏃剁敓鏁堛€?

msgid "Set the default domain resolution strategy for the sing-box node."
msgstr "涓?sing-box 鑺傜偣璁剧疆榛樿鐨勫煙鍚嶈В鏋愮瓥鐣ャ€?

msgid "Total Lines"
msgstr "鎬昏鏁帮細"

msgid "Read List"
msgstr "璇诲彇鍒楄〃"

msgid "Maintain"
msgstr "缁存姢"

msgid "Backup and Restore"
msgstr "澶囦唤杩樺師"

msgid "Backup or Restore Client and Server Configurations."
msgstr "澶囦唤鎴栬繕鍘熷鎴风鍙婃湇鍔＄閰嶇疆銆?

msgid "Note: Restoring configurations across different versions may cause compatibility issues."
msgstr "娉ㄦ剰锛氫笉鍚岀増鏈棿鐨勯厤缃仮澶嶅彲鑳戒細瀵艰嚧鍏煎鎬ч棶棰樸€?

msgid "Create Backup File"
msgstr "鍒涘缓澶囦唤鏂囦欢"

msgid "Restore Backup File"
msgstr "鎭㈠澶囦唤鏂囦欢"

msgid "DL Backup"
msgstr "涓嬭浇澶囦唤"

msgid "RST Backup"
msgstr "鎭㈠澶囦唤"

msgid "UL Restore"
msgstr "涓婁紶鎭㈠"

msgid "CLOSE WIN"
msgstr "鍏抽棴绐楀彛"

msgid "Restore to default configuration"
msgstr "鎭㈠榛樿閰嶇疆"

msgid "Do Reset"
msgstr "鎵ц閲嶇疆"

msgid "Please select a file first."
msgstr "璇峰厛閫夋嫨涓€涓枃浠躲€?

msgid "Invalid file type. Please upload a .tar.gz file."
msgstr "鏂囦欢绫诲瀷鏃犳晥锛岃涓婁紶涓€涓?.tar.gz 鏂囦欢銆?

msgid "File size exceeds 10MB limit."
msgstr "鏂囦欢澶у皬瓒呰繃 10MB 闄愬埗銆?

msgid "Do you want to restore the client to default settings?"
msgstr "鏄惁瑕佹仮澶嶅鎴风榛樿閰嶇疆锛?

msgid "Are you sure you want to restore the client to default settings?"
msgstr "鏄惁鐪熺殑瑕佹仮澶嶅鎴风榛樿閰嶇疆锛?

msgid "Settings have been successfully saved and applied!"
msgstr "璁剧疆宸叉垚鍔熶繚瀛樺苟搴旂敤!"

msgid "_urltest"
msgstr "URLTest"

msgid "URLTest node list"
msgstr "URLTest 鑺傜偣鍒楄〃"

msgid "List of nodes to test, <a target='_blank' href='https://sing-box.sagernet.org/configuration/outbound/urltest'>document</a>"
msgstr "瑕佹祴璇曠殑鑺傜偣鍒楄〃锛?a target='_blank' href='https://sing-box.sagernet.org/zh/configuration/outbound/urltest'>鏂囨。鍘熺悊</a>"

msgid "Test interval"
msgstr "娴嬭瘯闂撮殧"

msgid "Test interval must be less or equal than idle timeout."
msgstr "娴嬭瘯闂撮殧鏃堕棿蹇呴』灏忎簬鎴栫瓑浜庣┖闂茶秴鏃舵椂闂淬€?

msgid "Test tolerance"
msgstr "娴嬭瘯瀹瑰樊"

msgid "The test tolerance in milliseconds."
msgstr "娴嬭瘯瀹瑰樊鏃堕棿锛堝崟浣嶏細姣锛夈€?

msgid "Idle timeout"
msgstr "绌洪棽瓒呮椂"

msgid "The idle timeout."
msgstr "绌洪棽瓒呮椂鏃堕棿銆?

msgid "Interrupt existing connections"
msgstr "涓柇鐜版湁杩炴帴"

msgid "Interrupt existing connections when the selected outbound has changed."
msgstr "褰撻€夋嫨鐨勫嚭绔欏彂鐢熷彉鍖栨椂涓柇鐜版湁杩炴帴銆?

msgid "Port hopping range"
msgstr "绔彛璺宠穬鑼冨洿"

msgid "Format as 1000:2000 or 1000-2000 Multiple groups are separated by commas (,)."
msgstr "鏍煎紡涓猴細1000:2000 鎴?1000-2000 澶氱粍鏃剁敤閫楀彿(,)闅斿紑銆?

msgid "Use Custom Config"
msgstr "浣跨敤鑷畾涔夐厤缃?

msgid "Custom Config"
msgstr "鑷畾涔夐厤缃?

msgid "Must be JSON text!"
msgstr "蹇呴』鏄?JSON 鏂囨湰鍐呭锛?

msgid "Geo View"
msgstr "Geo 鏌ヨ"

msgid "Query"
msgstr "鏌ヨ"

msgid "Querying"
msgstr "鏌ヨ涓?

msgid "Please enter query content!"
msgstr "璇疯緭鍏ユ煡璇㈠唴瀹癸紒"

msgid "No results were found!"
msgstr "鏈壘鍒颁换浣曠粨鏋滐紒"

msgid "Domain/IP Query"
msgstr "鍩熷悕/IP 鏌ヨ"

msgid "GeoIP/Geosite Query"
msgstr "GeoIP/Geosite 鏌ヨ"

msgid "Enter a domain or IP to query the Geo rule list they belong to."
msgstr "杈撳叆鍩熷悕/IP锛屾煡璇㈠畠浠墍鍦ㄧ殑 Geo 瑙勫垯鍒楄〃銆?

msgid "Enter a GeoIP or Geosite to extract the domains/IPs they contain. Format: geoip:cn or geosite:gfw"
msgstr "杈撳叆 GeoIP/Geosite锛屾彁鍙栧畠浠墍鍖呭惈鐨勫煙鍚?IP銆傛牸寮忥細geoip:cn 鎴?geosite:gfw"

msgid "Tips:"
msgstr "灏忚创澹細"

msgid "By entering a domain or IP, you can query the Geo rule list they belong to."
msgstr "鍙互閫氳繃杈撳叆鍩熷悕/IP锛屾煡璇㈠畠浠墍鍦ㄧ殑 Geo 瑙勫垯鍒楄〃銆?

msgid "By entering a GeoIP or Geosite, you can extract the domains/IPs they contain."
msgstr "鍙互閫氳繃杈撳叆 GeoIP/Geosite锛屾彁鍙栧畠浠墍鍖呭惈鐨勫煙鍚?IP銆?

msgid "Use the GeoIP/Geosite query function to verify if the entered Geo rules are correct."
msgstr "鍒╃敤 GeoIP/Geosite 鏌ヨ鍔熻兘锛屽彲浠ラ獙璇佽緭鍏ョ殑 Geo 瑙勫垯鏄惁姝ｇ‘銆?

