﻿#!/bin/bash

# XQNetwork 鍗歌浇鑴氭湰

set -e

# 棰滆壊瀹氫箟
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 鎵撳嵃甯﹂鑹茬殑娑堟伅
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 妫€鏌ユ槸鍚︿负 root 鐢ㄦ埛
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "璇蜂娇鐢?root 鏉冮檺杩愯姝よ剼鏈?
        exit 1
    fi
}

# 鍋滄鏈嶅姟
stop_services() {
    print_info "鍋滄 XQNetwork 鏈嶅姟..."
    
    /etc/init.d/xqnetwork stop 2>/dev/null || true
    /etc/init.d/xqnetwork_server stop 2>/dev/null || true
    
    # 鍋滄鏃ュ織杩囨护杩涚▼
    if [ -f "/tmp/etc/xqnetwork/log_filter.pid" ]; then
        local pid=$(cat /tmp/etc/xqnetwork/log_filter.pid)
        if [ -n "$pid" ]; then
            kill -9 "$pid" 2>/dev/null || true
        fi
        rm -f /tmp/etc/xqnetwork/log_filter.pid
    fi
    
    print_success "鏈嶅姟宸插仠姝?
}

# 澶囦唤閰嶇疆
backup_config() {
    print_info "鏄惁澶囦唤閰嶇疆鏂囦欢锛?y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        local backup_dir="/tmp/xqnetwork_uninstall_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        if [ -f "/etc/config/xqnetwork" ]; then
            cp "/etc/config/xqnetwork" "$backup_dir/"
        fi
        
        if [ -f "/etc/config/xqnetwork_server" ]; then
            cp "/etc/config/xqnetwork_server" "$backup_dir/"
        fi
        
        print_success "閰嶇疆宸插浠藉埌: $backup_dir"
    fi
}

# 鍒犻櫎鏂囦欢
remove_files() {
    print_info "鍒犻櫎 XQNetwork 鏂囦欢..."
    
    # LuCI 鏂囦欢
    rm -rf /usr/lib/lua/luci/controller/xqnetwork.lua 2>/dev/null || true
    rm -rf /usr/lib/lua/luci/model/cbi/xqnetwork/ 2>/dev/null || true
    rm -rf /usr/lib/lua/luci/view/xqnetwork/ 2>/dev/null || true
    rm -rf /usr/lib/lua/luci/xqnetwork/ 2>/dev/null || true
    
    # 搴旂敤鏂囦欢
    rm -rf /usr/share/xqnetwork/ 2>/dev/null || true
    rm -rf /etc/init.d/xqnetwork 2>/dev/null || true
    rm -rf /etc/init.d/xqnetwork_server 2>/dev/null || true
    
    # 涓存椂鏂囦欢
    rm -rf /tmp/etc/xqnetwork* 2>/dev/null || true
    rm -rf /tmp/log/xqnetwork* 2>/dev/null || true
    
    # Web 鏂囦欢
    rm -rf /www/luci-static/resources/qrcode.min.js 2>/dev/null || true
    
    print_success "鏂囦欢鍒犻櫎瀹屾垚"
}

# 鍒犻櫎閰嶇疆
remove_config() {
    print_info "鏄惁鍒犻櫎閰嶇疆鏂囦欢锛?y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -f /etc/config/xqnetwork 2>/dev/null || true
        rm -f /etc/config/xqnetwork_server 2>/dev/null || true
        print_success "閰嶇疆鏂囦欢宸插垹闄?
    else
        print_info "淇濈暀閰嶇疆鏂囦欢"
    fi
}

# 娓呯悊闃茬伀澧欒鍒?cleanup_firewall() {
    print_info "娓呯悊闃茬伀澧欒鍒?.."
    
    # 娓呯悊 iptables 瑙勫垯
    iptables -t nat -F PSW 2>/dev/null || true
    iptables -t nat -X PSW 2>/dev/null || true
    iptables -t mangle -F PSW 2>/dev/null || true
    iptables -t mangle -X PSW 2>/dev/null || true
    iptables -F PSW-SERVER 2>/dev/null || true
    iptables -X PSW-SERVER 2>/dev/null || true
    
    # 娓呯悊 ip6tables 瑙勫垯
    ip6tables -t nat -F PSW 2>/dev/null || true
    ip6tables -t nat -X PSW 2>/dev/null || true
    ip6tables -t mangle -F PSW 2>/dev/null || true
    ip6tables -t mangle -X PSW 2>/dev/null || true
    ip6tables -F PSW-SERVER 2>/dev/null || true
    ip6tables -X PSW-SERVER 2>/dev/null || true
    
    # 娓呯悊 nftables 瑙勫垯
    nft delete table inet fw4 2>/dev/null || true
    
    # 娓呯悊 ipset
    ipset destroy xqnetwork_vpslist 2>/dev/null || true
    ipset destroy xqnetwork_whitelist 2>/dev/null || true
    ipset destroy xqnetwork_blacklist 2>/dev/null || true
    
    print_success "闃茬伀澧欒鍒欐竻鐞嗗畬鎴?
}

# 閲嶅惎鏈嶅姟
restart_services() {
    print_info "閲嶅惎鐩稿叧鏈嶅姟..."
    
    # 閲嶅惎 LuCI
    /etc/init.d/uhttpd restart
    
    # 娓呴櫎 LuCI 缂撳瓨
    rm -rf /tmp/luci-*
    
    # 閲嶅惎缃戠粶锛堝彲閫夛級
    print_info "鏄惁閲嶅惎缃戠粶鏈嶅姟锛?y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        /etc/init.d/network restart
        print_info "缃戠粶鏈嶅姟宸查噸鍚?
    fi
    
    print_success "鏈嶅姟閲嶅惎瀹屾垚"
}

# 鏄剧ず鍗歌浇瀹屾垚淇℃伅
show_completion_info() {
    print_success "XQNetwork 鍗歌浇瀹屾垚锛?
    echo
    print_info "宸叉竻鐞嗙殑鍐呭锛?
    echo "- XQNetwork 搴旂敤鏂囦欢"
    echo "- LuCI 鐣岄潰鏂囦欢"
    echo "- 涓存椂鏂囦欢鍜屾棩蹇?
    echo "- 闃茬伀澧欒鍒?
    echo "- 杩涚▼鍜屾湇鍔?
    echo
    print_warning "娉ㄦ剰锛?
    echo "- 濡傛灉淇濈暀浜嗛厤缃枃浠讹紝涓嬫瀹夎鏃朵細鑷姩浣跨敤"
    echo "- 寤鸿閲嶅惎璺敱鍣ㄤ互纭繚瀹屽叏娓呯悊"
    echo
}

# 涓诲嚱鏁?main() {
    echo "========================================"
    echo "       XQNetwork 鍗歌浇鑴氭湰"
    echo "========================================"
    echo
    
    check_root
    
    print_warning "纭畾瑕佸嵏杞?XQNetwork 鍚楋紵(y/n)"
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_info "鍙栨秷鍗歌浇"
        exit 0
    fi
    
    stop_services
    backup_config
    remove_files
    remove_config
    cleanup_firewall
    restart_services
    show_completion_info
    
    print_success "鍗歌浇瀹屾垚锛佸缓璁噸鍚矾鐢卞櫒銆?
}

# 杩愯涓诲嚱鏁?main "$@"

