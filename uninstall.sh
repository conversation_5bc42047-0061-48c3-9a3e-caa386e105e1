#!/bin/bash

# XQNetwork 卸载脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用 root 权限运行此脚本"
        exit 1
    fi
}

# 停止服务
stop_services() {
    print_info "停止 XQNetwork 服务..."
    
    /etc/init.d/passwall stop 2>/dev/null || true
    /etc/init.d/passwall_server stop 2>/dev/null || true
    
    # 停止日志过滤进程
    if [ -f "/tmp/etc/passwall/log_filter.pid" ]; then
        local pid=$(cat /tmp/etc/passwall/log_filter.pid)
        if [ -n "$pid" ]; then
            kill -9 "$pid" 2>/dev/null || true
        fi
        rm -f /tmp/etc/passwall/log_filter.pid
    fi
    
    print_success "服务已停止"
}

# 备份配置
backup_config() {
    print_info "是否备份配置文件？(y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        local backup_dir="/tmp/xqnetwork_uninstall_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        if [ -f "/etc/config/passwall" ]; then
            cp "/etc/config/passwall" "$backup_dir/"
        fi
        
        if [ -f "/etc/config/passwall_server" ]; then
            cp "/etc/config/passwall_server" "$backup_dir/"
        fi
        
        print_success "配置已备份到: $backup_dir"
    fi
}

# 删除文件
remove_files() {
    print_info "删除 XQNetwork 文件..."
    
    # LuCI 文件
    rm -rf /usr/lib/lua/luci/controller/passwall.lua 2>/dev/null || true
    rm -rf /usr/lib/lua/luci/model/cbi/passwall/ 2>/dev/null || true
    rm -rf /usr/lib/lua/luci/view/passwall/ 2>/dev/null || true
    rm -rf /usr/lib/lua/luci/passwall/ 2>/dev/null || true
    
    # 应用文件
    rm -rf /usr/share/passwall/ 2>/dev/null || true
    rm -rf /etc/init.d/passwall 2>/dev/null || true
    rm -rf /etc/init.d/passwall_server 2>/dev/null || true
    
    # 临时文件
    rm -rf /tmp/etc/passwall* 2>/dev/null || true
    rm -rf /tmp/log/passwall* 2>/dev/null || true
    
    # Web 文件
    rm -rf /www/luci-static/resources/qrcode.min.js 2>/dev/null || true
    
    print_success "文件删除完成"
}

# 删除配置
remove_config() {
    print_info "是否删除配置文件？(y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -f /etc/config/passwall 2>/dev/null || true
        rm -f /etc/config/passwall_server 2>/dev/null || true
        print_success "配置文件已删除"
    else
        print_info "保留配置文件"
    fi
}

# 清理防火墙规则
cleanup_firewall() {
    print_info "清理防火墙规则..."
    
    # 清理 iptables 规则
    iptables -t nat -F PSW 2>/dev/null || true
    iptables -t nat -X PSW 2>/dev/null || true
    iptables -t mangle -F PSW 2>/dev/null || true
    iptables -t mangle -X PSW 2>/dev/null || true
    iptables -F PSW-SERVER 2>/dev/null || true
    iptables -X PSW-SERVER 2>/dev/null || true
    
    # 清理 ip6tables 规则
    ip6tables -t nat -F PSW 2>/dev/null || true
    ip6tables -t nat -X PSW 2>/dev/null || true
    ip6tables -t mangle -F PSW 2>/dev/null || true
    ip6tables -t mangle -X PSW 2>/dev/null || true
    ip6tables -F PSW-SERVER 2>/dev/null || true
    ip6tables -X PSW-SERVER 2>/dev/null || true
    
    # 清理 nftables 规则
    nft delete table inet fw4 2>/dev/null || true
    
    # 清理 ipset
    ipset destroy passwall_vpslist 2>/dev/null || true
    ipset destroy passwall_whitelist 2>/dev/null || true
    ipset destroy passwall_blacklist 2>/dev/null || true
    
    print_success "防火墙规则清理完成"
}

# 重启服务
restart_services() {
    print_info "重启相关服务..."
    
    # 重启 LuCI
    /etc/init.d/uhttpd restart
    
    # 清除 LuCI 缓存
    rm -rf /tmp/luci-*
    
    # 重启网络（可选）
    print_info "是否重启网络服务？(y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        /etc/init.d/network restart
        print_info "网络服务已重启"
    fi
    
    print_success "服务重启完成"
}

# 显示卸载完成信息
show_completion_info() {
    print_success "XQNetwork 卸载完成！"
    echo
    print_info "已清理的内容："
    echo "- XQNetwork 应用文件"
    echo "- LuCI 界面文件"
    echo "- 临时文件和日志"
    echo "- 防火墙规则"
    echo "- 进程和服务"
    echo
    print_warning "注意："
    echo "- 如果保留了配置文件，下次安装时会自动使用"
    echo "- 建议重启路由器以确保完全清理"
    echo
}

# 主函数
main() {
    echo "========================================"
    echo "       XQNetwork 卸载脚本"
    echo "========================================"
    echo
    
    check_root
    
    print_warning "确定要卸载 XQNetwork 吗？(y/n)"
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_info "取消卸载"
        exit 0
    fi
    
    stop_services
    backup_config
    remove_files
    remove_config
    cleanup_firewall
    restart_services
    show_completion_info
    
    print_success "卸载完成！建议重启路由器。"
}

# 运行主函数
main "$@"
