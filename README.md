# luci-app-xqnetwork

基于 openwrt-passwall 改造的 XQNetwork 应用，专门用于对接 V2Board 面板。

## 主要特性

- **V2Board 集成**: 直接登录 V2Board 面板，自动拉取订阅
- **节点信息隐藏**: 自动隐藏节点的 IP 地址、域名等敏感信息
- **日志过滤**: 实时过滤日志中的敏感信息，保护用户隐私
- **简化界面**: 去除不必要的功能，专注于核心代理功能

## 功能说明

### V2Board 登录
- 支持邮箱密码登录
- 自动管理 Token 和过期时间
- 显示用户信息（套餐、流量等）

### 节点管理
- 自动从 V2Board 同步节点
- 隐藏节点真实 IP 和域名
- 支持多种代理协议

### 隐私保护
- 节点列表中隐藏敏感信息
- 日志文件实时过滤
- 防止信息泄露

## 安装说明

1. 将 `luci-app-xqnetwork` 目录复制到 OpenWrt 源码的 `package/` 目录下
2. 在 OpenWrt 配置中选择 `luci-app-xqnetwork`
3. 编译并安装到路由器

## 使用方法

1. 在 LuCI 界面中找到 "XQNetwork" 菜单
2. 进入 "V2Board Login" 页面
3. 填写 V2Board 面板地址、邮箱和密码
4. 点击登录并同步节点
5. 在节点列表中选择节点使用

## 注意事项

- 本应用基于 openwrt-passwall 改造，保留了核心代理功能
- 专门针对 V2Board 面板优化，其他面板可能不兼容
- 节点信息隐藏功能仅对 V2Board 节点生效
- 建议定期更新以获得最新功能和安全修复

## 原项目说明

本项目基于 [openwrt-passwall](https://github.com/xiaorouji/openwrt-passwall) 改造。

### 原项目注意事项
由于 Sing-box 在 1.12.0 版本中移除 Geo 只保留规则集，Passwall 为适应这一变更，同时兼容 Xray 和 Sing-box 的分流方式，从 25.3.9 版起，Sing-box 分流将依赖 Geoview 从 Geofile 生成规则集。**未安装 Geoview 将无法使用 Sing-box 分流**。

由于 Geoview v0.1.9 及之前版本在转换规则时的一些问题，**请务必将 Geoview 更新到 v0.1.10 版**。
