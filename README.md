# luci-app-xqnetwork

基于 openwrt-passwall 改造的 XQNetwork 应用，专门用于对接面板 API。

## 主要特性

- **API 集成**: 直接登录面板，自动拉取订阅
- **多 API 地址支持**: 支持多个 API 地址自动重试
- **节点信息隐藏**: 自动隐藏节点的 IP 地址、域名等敏感信息
- **日志过滤**: 实时过滤日志中的敏感信息，保护用户隐私
- **简化界面**: 去除不必要的功能，专注于核心代理功能
- **配置文件管理**: 通过 OSS 配置文件动态获取 API 地址

## 功能说明

### API 登录
- 支持邮箱密码登录
- 自动管理 Token 和过期时间
- 显示用户信息（套餐、流量等）
- 多 API 地址自动重试机制

### 节点管理
- 自动从面板同步节点
- 隐藏节点真实 IP 和域名
- 支持多种代理协议
- 使用自定义请求头标识

### 隐私保护
- 节点列表中隐藏敏感信息
- 日志文件实时过滤
- 防止信息泄露

## 安装说明

1. 将 `luci-app-xqnetwork` 目录复制到 OpenWrt 源码的 `package/` 目录下
2. 在 OpenWrt 配置中选择 `luci-app-xqnetwork`
3. 编译并安装到路由器

## 使用方法

1. 在 LuCI 界面中找到 "XQNetwork" 菜单
2. 进入 "XQNetwork Login" 页面
3. 填写邮箱和密码
4. 点击登录并同步节点
5. 在节点列表中选择节点使用

## 注意事项

- 本应用基于 openwrt-passwall 改造，保留了核心代理功能
- API 地址通过 OSS 配置文件动态获取，支持多地址重试
- 节点信息隐藏功能仅对 XQNetwork 节点生效
- 使用自定义请求头 "xqnetworkpasswall" 标识
- 建议定期更新以获得最新功能和安全修复

## 配置说明

### API 地址配置
API 地址通过 OSS 配置文件获取：
- 配置文件地址：`/usr/share/xqnetwork/config.json`
- OSS 文件格式：base64 编码的 JSON
- 支持多个 API 地址自动重试

### 示例配置
OSS 文件内容（base64 编码前）：
```json
{
  "url": [
    "https://api1.example.com",
    "https://api2.example.com",
    "https://api3.example.com"
  ]
}
```

## 原项目说明

本项目基于 [openwrt-passwall](https://github.com/xiaorouji/openwrt-passwall) 改造。

### 原项目注意事项
由于 Sing-box 在 1.12.0 版本中移除 Geo 只保留规则集，Passwall 为适应这一变更，同时兼容 Xray 和 Sing-box 的分流方式，从 25.3.9 版起，Sing-box 分流将依赖 Geoview 从 Geofile 生成规则集。**未安装 Geoview 将无法使用 Sing-box 分流**。

由于 Geoview v0.1.9 及之前版本在转换规则时的一些问题，**请务必将 Geoview 更新到 v0.1.10 版**。
