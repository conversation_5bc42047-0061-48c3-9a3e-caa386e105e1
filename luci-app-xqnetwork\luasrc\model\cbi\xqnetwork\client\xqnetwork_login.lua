local api = require "luci.xqnetwork.api"
local appname = "xqnetwork"

m = Map(appname, translate("XQNetwork Login"))
api.set_apply_on_parse(m)

-- 创建 xqnetwork 配置段（如果不存在）
if not m:get("@xqnetwork[0]") then
	m:section("xqnetwork", "xqnetwork")
end

s = m:section(NamedSection, "@xqnetwork[0]", "xqnetwork", translate("XQNetwork Configuration"))
s.anonymous = true
s.addremove = false

-- 邮箱
o = s:option(Value, "email", translate("Email"))
o.placeholder = "<EMAIL>"
o.description = translate("Your XQNetwork account email")
o.rmempty = false

-- 密码
o = s:option(Value, "password", translate("Password"))
o.password = true
o.description = translate("Your XQNetwork account password")
o.rmempty = false

-- 登录状态显示
o = s:option(DummyValue, "login_status", translate("Login Status"))
o.rawhtml = true
o.template = "xqnetwork/xqnetwork_status"

-- 用户信息显示
o = s:option(DummyValue, "user_info", translate("User Information"))
o.rawhtml = true
o.template = "xqnetwork/xqnetwork_user_info"

-- 操作按钮
o = s:option(DummyValue, "actions", translate("Actions"))
o.rawhtml = true
o.template = "xqnetwork/xqnetwork_actions"

return m
