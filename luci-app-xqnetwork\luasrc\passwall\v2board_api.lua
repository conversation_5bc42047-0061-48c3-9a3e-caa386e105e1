module("luci.passwall.v2board_api", package.seeall)

local api = require "luci.passwall.api"
local jsonc = require "luci.jsonc"
local sys = require "luci.sys"
local uci = require "luci.model.uci".cursor()
local fs = require "nixio.fs"

local appname = "passwall"

-- V2Board API 配置
local V2BOARD_CONFIG = {
    base_url = "",
    email = "",
    password = "",
    token = "",
    token_expire = 0
}

-- 日志函数
local function log(msg)
    api.log(msg, "V2Board")
end

-- 获取配置
local function get_config()
    V2BOARD_CONFIG.base_url = uci:get(appname, "@v2board[0]", "base_url") or ""
    V2BOARD_CONFIG.email = uci:get(appname, "@v2board[0]", "email") or ""
    V2BOARD_CONFIG.password = uci:get(appname, "@v2board[0]", "password") or ""
    V2BOARD_CONFIG.token = uci:get(appname, "@v2board[0]", "token") or ""
    V2BOARD_CONFIG.token_expire = tonumber(uci:get(appname, "@v2board[0]", "token_expire") or "0")
end

-- 保存配置
local function save_config()
    uci:set(appname, "@v2board[0]", "base_url", V2BOARD_CONFIG.base_url)
    uci:set(appname, "@v2board[0]", "email", V2BOARD_CONFIG.email)
    uci:set(appname, "@v2board[0]", "password", V2BOARD_CONFIG.password)
    uci:set(appname, "@v2board[0]", "token", V2BOARD_CONFIG.token)
    uci:set(appname, "@v2board[0]", "token_expire", tostring(V2BOARD_CONFIG.token_expire))
    uci:commit(appname)
end

-- HTTP 请求函数
local function http_request(url, method, data, headers)
    method = method or "GET"
    headers = headers or {}
    
    local curl_args = {"-skfL", "--connect-timeout 10", "--retry 2"}
    
    -- 添加请求头
    for k, v in pairs(headers) do
        table.insert(curl_args, "-H")
        table.insert(curl_args, k .. ": " .. v)
    end
    
    -- 添加请求方法和数据
    if method == "POST" then
        table.insert(curl_args, "-X POST")
        if data then
            table.insert(curl_args, "-d")
            table.insert(curl_args, data)
            table.insert(curl_args, "-H")
            table.insert(curl_args, "Content-Type: application/json")
        end
    end
    
    local return_code, result = api.curl_base(url, nil, curl_args)
    
    if return_code == 0 and result then
        local json_data = jsonc.parse(result)
        return json_data
    else
        log("HTTP请求失败: " .. url .. " 返回码: " .. tostring(return_code))
        return nil
    end
end

-- 检查 token 是否有效
local function is_token_valid()
    if not V2BOARD_CONFIG.token or V2BOARD_CONFIG.token == "" then
        return false
    end
    
    local current_time = os.time()
    if V2BOARD_CONFIG.token_expire > 0 and current_time >= V2BOARD_CONFIG.token_expire then
        return false
    end
    
    return true
end

-- 登录到 V2Board
function login(base_url, email, password)
    if not base_url or not email or not password then
        return false, "缺少必要的登录参数"
    end
    
    -- 确保 URL 格式正确
    if not base_url:match("^https?://") then
        base_url = "https://" .. base_url
    end
    if base_url:sub(-1) == "/" then
        base_url = base_url:sub(1, -2)
    end
    
    local login_url = base_url .. "/api/v1/passport/auth/login"
    local login_data = jsonc.stringify({
        email = email,
        password = password
    })
    
    log("尝试登录到 V2Board: " .. base_url)
    
    local response = http_request(login_url, "POST", login_data)
    
    if response and response.data and response.data.auth_data then
        V2BOARD_CONFIG.base_url = base_url
        V2BOARD_CONFIG.email = email
        V2BOARD_CONFIG.password = password
        V2BOARD_CONFIG.token = response.data.auth_data
        -- 设置 token 过期时间为 24 小时后
        V2BOARD_CONFIG.token_expire = os.time() + 86400
        
        save_config()
        log("登录成功")
        return true, "登录成功"
    else
        local error_msg = "登录失败"
        if response and response.message then
            error_msg = error_msg .. ": " .. response.message
        end
        log(error_msg)
        return false, error_msg
    end
end

-- 获取用户信息
function get_user_info()
    get_config()
    
    if not is_token_valid() then
        return nil, "Token 无效或已过期，请重新登录"
    end
    
    local user_url = V2BOARD_CONFIG.base_url .. "/api/v1/user/info"
    local headers = {
        ["Authorization"] = V2BOARD_CONFIG.token
    }
    
    local response = http_request(user_url, "GET", nil, headers)
    
    if response and response.data then
        return response.data, "获取用户信息成功"
    else
        local error_msg = "获取用户信息失败"
        if response and response.message then
            error_msg = error_msg .. ": " .. response.message
        end
        return nil, error_msg
    end
end

-- 获取订阅链接
function get_subscribe_url()
    get_config()
    
    if not is_token_valid() then
        return nil, "Token 无效或已过期，请重新登录"
    end
    
    local subscribe_url = V2BOARD_CONFIG.base_url .. "/api/v1/client/subscribe?token=" .. V2BOARD_CONFIG.token
    return subscribe_url, "获取订阅链接成功"
end

-- 获取节点列表（通过订阅）
function get_nodes()
    local subscribe_url, msg = get_subscribe_url()
    if not subscribe_url then
        return nil, msg
    end

    log("获取节点列表: " .. subscribe_url)

    local curl_args = {"-skfL", "--connect-timeout 10", "--retry 2"}
    local return_code, result = api.curl_base(subscribe_url, nil, curl_args)

    if return_code == 0 and result then
        -- 解析订阅内容，通常是 base64 编码的节点信息
        local decoded_content

        -- 尝试 base64 解码
        local success, decoded = pcall(function()
            return api.bin.b64decode(result)
        end)

        if success and decoded then
            decoded_content = decoded
        else
            -- 如果不是 base64 编码，直接返回原始内容
            decoded_content = result
        end

        return decoded_content, "获取节点列表成功"
    else
        return nil, "获取节点列表失败"
    end
end

-- 隐藏节点敏感信息的函数
function hide_node_info(node_data)
    if not node_data then
        return node_data
    end

    -- 隐藏 IP 地址
    if node_data.address then
        -- 将 IP 地址替换为星号
        if api.datatypes.ipaddr(node_data.address) then
            local parts = {}
            for part in node_data.address:gmatch("[^%.]+") do
                table.insert(parts, part)
            end
            if #parts == 4 then
                node_data.address = parts[1] .. ".***.***.***"
            end
        elseif api.datatypes.ip6addr(node_data.address) then
            -- IPv6 地址隐藏
            local parts = {}
            for part in node_data.address:gmatch("[^:]+") do
                table.insert(parts, part)
            end
            if #parts >= 2 then
                node_data.address = parts[1] .. ":***::***"
            end
        else
            -- 域名隐藏
            local domain_parts = {}
            for part in node_data.address:gmatch("[^%.]+") do
                table.insert(domain_parts, part)
            end
            if #domain_parts >= 2 then
                node_data.address = "***." .. domain_parts[#domain_parts]
            end
        end
    end

    -- 隐藏其他敏感信息
    if node_data.server then
        node_data.server = node_data.address
    end

    return node_data
end

-- 检查登录状态
function check_login_status()
    get_config()
    
    if not V2BOARD_CONFIG.base_url or V2BOARD_CONFIG.base_url == "" then
        return false, "未配置 V2Board 地址"
    end
    
    if not is_token_valid() then
        return false, "Token 无效或已过期"
    end
    
    -- 尝试获取用户信息来验证登录状态
    local user_info, msg = get_user_info()
    if user_info then
        return true, "已登录"
    else
        return false, msg
    end
end

-- 登出
function logout()
    V2BOARD_CONFIG.token = ""
    V2BOARD_CONFIG.token_expire = 0
    save_config()
    log("已登出")
    return true, "登出成功"
end
