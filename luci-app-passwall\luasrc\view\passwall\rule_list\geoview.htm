<%
local api = require "luci.passwall.api"
-%>

<style>
	.faq-title {
		color: var(--primary);
		font-weight: bolder;
		margin-bottom: 0.5rem;
		display: inline-block;
	}
	.faq-item {
		margin-bottom: 0.8rem;
		line-height:1.2rem;
	}
</style>

<div class="cbi-value">
	<ul>
		<b class="faq-title"><%:Tips:%></b>
		<li class="faq-item">1. <span><%:By entering a domain or IP, you can query the Geo rule list they belong to.%></span></li>
		<li class="faq-item">2. <span><%:By entering a GeoIP or Geosite, you can extract the domains/IPs they contain.%></span></li>
		<li class="faq-item">3. <span><%:Use the GeoIP/Geosite query function to verify if the entered Geo rules are correct.%></span></li>
	</ul>
</div>
<div class="cbi-value" id="cbi-passwall-geoview-lookup"><label class="cbi-value-title" for="geoview.lookup"><%:Domain/IP Query%></label>
	<div class="cbi-value-field">
	<input type="text" class="cbi-textfield" id="geoview.lookup" name="geoview.lookup" />
	<input class="btn cbi-button cbi-button-apply" type="button" id="lookup-view_btn"
		onclick='do_geoview(this, "lookup", document.getElementById("geoview.lookup").value)'
		value="<%:Query%>" />
	<br />
	<div class="cbi-value-description">
		<%:Enter a domain or IP to query the Geo rule list they belong to.%>
	</div>
	</div>
</div>
<div class="cbi-value" id="cbi-passwall-geoview-extract"><label class="cbi-value-title" for="geoview.extract"><%:GeoIP/Geosite Query%></label>
	<div class="cbi-value-field">
	<input type="text" class="cbi-textfield" id="geoview.extract" name="geoview.extract" />
	<input class="btn cbi-button cbi-button-apply" type="button" id="extract-view_btn"
		onclick='do_geoview(this, "extract", document.getElementById("geoview.extract").value)'
		value="<%:Query%>" />
	<br />
	<div class="cbi-value-description">
		<%:Enter a GeoIP or Geosite to extract the domains/IPs they contain. Format: geoip:cn or geosite:gfw%>
	</div>
	</div>
</div>
<div class="cbi-value">
	<textarea id="geoview_textarea" class="cbi-input-textarea" style="width: 100%; margin-top: 10px;" rows="25" wrap="off" readonly="readonly"></textarea>
</div>

<script type="text/javascript">
	//<![CDATA[
	var lookup_btn = document.getElementById("lookup-view_btn");
	var extract_btn = document.getElementById("extract-view_btn");
	var QueryText = '<%:Query%>';
	var QueryingText = '<%:Querying%>';

	function do_geoview(btn,action,value) {
		value = value.trim();
		if (!value) {
			alert("<%:Please enter query content!%>");
			return;
        }
		lookup_btn.disabled = true;
		extract_btn.disabled = true;
		btn.value = QueryingText;
		var textarea = document.getElementById('geoview_textarea');
		textarea.textContent = "";
		fetch('<%= api.url("geo_view") %>?action=' + action + '&value=' + encodeURIComponent(value))
			.then(response => response.text())
			.then(data => {
				textarea.textContent = data;
				lookup_btn.disabled = false;
				extract_btn.disabled = false;
				btn.value = QueryText;
			})
	}

	document.getElementById("geoview.lookup").addEventListener("keydown", function(event) {
		if (event.key === "Enter") {
			event.preventDefault();
			lookup_btn.click();
		}
	});

	document.getElementById("geoview.extract").addEventListener("keydown", function(event) {
		if (event.key === "Enter") {
			event.preventDefault();
			extract_btn.click();
		}
	});
	//]]>
</script>
