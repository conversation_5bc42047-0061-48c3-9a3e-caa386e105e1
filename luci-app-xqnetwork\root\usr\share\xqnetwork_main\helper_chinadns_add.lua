﻿local sys = require "luci.sys"
local api = require "luci.xqnetwork.api"
local appname = "xqnetwork"

local var = api.get_args(arg)
local FLAG = var["-FLAG"]
local LISTEN_PORT = var["-LISTEN_PORT"]
local DNS_LOCAL = var["-DNS_LOCAL"]
local DNS_TRUST = var["-DNS_TRUST"]
local USE_DIRECT_LIST = var["-USE_DIRECT_LIST"]
local USE_PROXY_LIST = var["-USE_PROXY_LIST"]
local USE_BLOCK_LIST = var["-USE_BLOCK_LIST"]
local GFWLIST = var["-GFWLIST"]
local CHNLIST = var["-CHNLIST"]
local NO_IPV6_TRUST = var["-NO_IPV6_TRUST"]
local DEFAULT_MODE = var["-DEFAULT_MODE"]
local DEFAULT_TAG = var["-DEFAULT_TAG"]
local NO_LOGIC_LOG = var["-NO_LOGIC_LOG"]
local TCP_NODE = var["-TCP_NODE"]
local NFTFLAG = var["-NFTFLAG"]
local REMOTE_FAKEDNS = var["-REMOTE_FAKEDNS"]
local LOG_FILE = var["-LOG_FILE"]

local uci = api.uci
local sys = api.sys
local fs = api.fs
local datatypes = api.datatypes

local TMP_PATH = "/tmp/etc/" .. appname
local TMP_ACL_PATH = TMP_PATH .. "/acl"
local RULES_PATH = "/usr/share/" .. appname .. "/rules"
local FLAG_PATH = TMP_ACL_PATH .. "/" .. FLAG
local config_lines = {}
local tmp_lines = {}
local USE_GEOVIEW = uci:get(appname, "@global_rules[0]", "enable_geoview")

local function log(...)
	if NO_LOGIC_LOG == "1" then
		return
	end
	api.log(...)
end

local function is_file_nonzero(path)
	if path and #path > 1 then
		if sys.exec('[ -s "%s" ] && echo -n 1' % path) == "1" then
			return true
		end
	end
	return nil
end

local function insert_unique(dest_table, value, lookup_table)
	if not lookup_table[value] then
		table.insert(dest_table, value)
		lookup_table[value] = true
	end
end

local function merge_array(array1, array2)
	for i, line in ipairs(array2) do
		table.insert(array1, #array1 + 1, line)
	end
end

local function insert_array_before(array1, array2, target) --灏哸rray2鎻掑叆鍒癮rray1鐨則arget鍓嶉潰锛宼arget涓嶅瓨鍦ㄥ垯杩藉姞
	for i, line in ipairs(array1) do
		if line == target then
			for j = #array2, 1, -1 do
				table.insert(array1, i, array2[j])
			end
			return
		end
	end
	merge_array(array1, array2)
end

local function insert_array_after(array1, array2, target) --灏哸rray2鎻掑叆鍒癮rray1鐨則arget鍚庨潰锛宼arget涓嶅瓨鍦ㄥ垯杩藉姞
	for i, line in ipairs(array1) do
		if line == target then
			for j = 1, #array2 do
				table.insert(array1, i + j, array2[j])
			end
			return
		end
	end
	merge_array(array1, array2)
end

local function get_geosite(list_arg, out_path)
	local geosite_path = uci:get(appname, "@global_rules[0]", "v2ray_location_asset") or "/usr/share/v2ray/"
	geosite_path = geosite_path:match("^(.*)/") .. "/geosite.dat"
	if not is_file_nonzero(geosite_path) then return 1 end
	if api.is_finded("geoview") and list_arg and out_path then
		sys.exec("geoview -type geosite -append=true -input " .. geosite_path .. " -list '" .. list_arg .. "' -output " .. out_path)
		return 0
	end
	return 1
end

if not fs.access(FLAG_PATH) then
	fs.mkdir(FLAG_PATH)
end

local setflag = (NFTFLAG == "1") and "inet@xqnetwork@" or ""

local only_global = (DEFAULT_MODE == "proxy" and CHNLIST == "0" and GFWLIST == "0") and 1

local force_https_soa = uci:get(appname, "@global[0]", "force_https_soa") or 1

config_lines = {
	LOG_FILE ~= "/dev/null" and "verbose" or "",
	"bind-addr ::",
	"bind-port " .. LISTEN_PORT,
	"china-dns " .. DNS_LOCAL,
	"trust-dns " .. DNS_TRUST,
	tonumber(force_https_soa) == 1 and "filter-qtype 65" or ""
}

for i = 1, 6 do
	table.insert(config_lines, "#--" .. i)
end

--鑷畾涔夎鍒欑粍锛屽悗澹版槑鐨勭粍鍏锋湁鏇撮珮浼樺厛绾?--灞忚斀鍒楄〃
local file_block_host = TMP_ACL_PATH .. "/block_host"
if USE_BLOCK_LIST == "1" and not fs.access(file_block_host) then
	local block_domain, lookup_block_domain = {}, {}
	local geosite_arg = ""
	for line in io.lines(RULES_PATH .. "/block_host") do
		if not line:find("#") and line:find("geosite:") then
			line = string.match(line, ":([^:]+)$")
			geosite_arg = geosite_arg .. (geosite_arg ~= "" and "," or "") .. line
		else
			line = api.get_std_domain(line)
			if line ~= "" and not line:find("#") then
				insert_unique(block_domain, line, lookup_block_domain)
			end
		end
	end
	if #block_domain > 0 then
		local f_out = io.open(file_block_host, "w")
		for i = 1, #block_domain do
			f_out:write(block_domain[i] .. "\n")
		end
		f_out:close()
	end
	if USE_GEOVIEW == "1" and geosite_arg ~= "" and api.is_finded("geoview") then
		if get_geosite(geosite_arg, file_block_host) == 0 then
			log("  * 瑙ｆ瀽[灞忚斀鍒楄〃] Geosite 鍒板睆钄藉煙鍚嶈〃(blocklist)瀹屾垚")
		else
			log("  * 瑙ｆ瀽[灞忚斀鍒楄〃] Geosite 鍒板睆钄藉煙鍚嶈〃(blocklist)澶辫触锛?)
		end
	end
end
if USE_BLOCK_LIST == "1" and is_file_nonzero(file_block_host) then
	tmp_lines = {
		"group null",
		"group-dnl " .. file_block_host
	}
	insert_array_after(config_lines, tmp_lines, "#--5")
end

--濮嬬粓鐢ㄥ浗鍐匘NS瑙ｆ瀽鑺傜偣鍩熷悕
local file_vpslist = TMP_ACL_PATH .. "/vpslist"
if not is_file_nonzero(file_vpslist) then
	local f_out = io.open(file_vpslist, "w")
	local written_domains = {}
	uci:foreach(appname, "nodes", function(t)
		local function process_address(address)
			if address == "engage.cloudflareclient.com" then return end
			if datatypes.hostname(address) and not written_domains[address] then
				f_out:write(address .. "\n")
				written_domains[address] = true
			end
		end
		process_address(t.address)
		process_address(t.download_address)
	end)
	f_out:close()
end
if is_file_nonzero(file_vpslist) then
	local sets = {
		setflag .. "xqnetwork_vps",
		setflag .. "xqnetwork_vps6"
	}
	tmp_lines = {
		"group vpslist",
		"group-dnl " .. file_vpslist,
		"group-upstream " .. DNS_LOCAL,
		"group-ipset " .. table.concat(sets, ",")
	}
	insert_array_after(config_lines, tmp_lines, "#--6")
	log(string.format("  - 鑺傜偣鍒楄〃涓殑鍩熷悕(vpslist)锛?s", DNS_LOCAL or "榛樿"))
end

--鐩磋繛锛堢櫧鍚嶅崟锛夊垪琛?local file_direct_host = TMP_ACL_PATH .. "/direct_host"
if USE_DIRECT_LIST == "1" and not fs.access(file_direct_host) then
	local direct_domain, lookup_direct_domain = {}, {}
	local geosite_arg = ""
	for line in io.lines(RULES_PATH .. "/direct_host") do
		if not line:find("#") and line:find("geosite:") then
			line = string.match(line, ":([^:]+)$")
			geosite_arg = geosite_arg .. (geosite_arg ~= "" and "," or "") .. line
		else
			line = api.get_std_domain(line)
			if line ~= "" and not line:find("#") then
				insert_unique(direct_domain, line, lookup_direct_domain)
			end
		end
	end
	if #direct_domain > 0 then
		local f_out = io.open(file_direct_host, "w")
		for i = 1, #direct_domain do
			f_out:write(direct_domain[i] .. "\n")
		end
		f_out:close()
	end
	if USE_GEOVIEW == "1" and geosite_arg ~= "" and api.is_finded("geoview") then
		if get_geosite(geosite_arg, file_direct_host) == 0 then
			log("  * 瑙ｆ瀽[鐩磋繛鍒楄〃] Geosite 鍒板煙鍚嶇櫧鍚嶅崟(whitelist)瀹屾垚")
		else
			log("  * 瑙ｆ瀽[鐩磋繛鍒楄〃] Geosite 鍒板煙鍚嶇櫧鍚嶅崟(whitelist)澶辫触锛?)
		end
	end
end
if USE_DIRECT_LIST == "1" and is_file_nonzero(file_direct_host) then
	local sets = {
		setflag .. "xqnetwork_white",
		setflag .. "xqnetwork_white6"
	}
	tmp_lines = {
		"group directlist",
		"group-dnl " .. file_direct_host,
		"group-upstream " .. DNS_LOCAL,
		"group-ipset " .. table.concat(sets, ",")
	}
	insert_array_after(config_lines, tmp_lines, "#--4")
	log(string.format("  - 鍩熷悕鐧藉悕鍗?whitelist)锛?s", DNS_LOCAL or "榛樿"))
end

--浠ｇ悊锛堥粦鍚嶅崟锛夊垪琛?local file_proxy_host = TMP_ACL_PATH .. "/proxy_host"
if USE_PROXY_LIST == "1" and not fs.access(file_proxy_host) then
	local proxy_domain, lookup_proxy_domain = {}, {}
	local geosite_arg = ""
	for line in io.lines(RULES_PATH .. "/proxy_host") do
		if not line:find("#") and line:find("geosite:") then
			line = string.match(line, ":([^:]+)$")
			geosite_arg = geosite_arg .. (geosite_arg ~= "" and "," or "") .. line
		else
			line = api.get_std_domain(line)
			if line ~= "" and not line:find("#") then
				insert_unique(proxy_domain, line, lookup_proxy_domain)
			end
		end
	end
	if #proxy_domain > 0 then
		local f_out = io.open(file_proxy_host, "w")
		for i = 1, #proxy_domain do
			f_out:write(proxy_domain[i] .. "\n")
		end
		f_out:close()
	end
	if USE_GEOVIEW == "1" and geosite_arg ~= "" and api.is_finded("geoview") then
		if get_geosite(geosite_arg, file_proxy_host) == 0 then
			log("  * 瑙ｆ瀽[浠ｇ悊鍒楄〃] Geosite 鍒颁唬鐞嗗煙鍚嶈〃(blacklist)瀹屾垚")
		else
			log("  * 瑙ｆ瀽[浠ｇ悊鍒楄〃] Geosite 鍒颁唬鐞嗗煙鍚嶈〃(blacklist)澶辫触锛?)
		end
	end
end
if USE_PROXY_LIST == "1" and is_file_nonzero(file_proxy_host) then
	local sets = {
		setflag .. "xqnetwork_black",
		setflag .. "xqnetwork_black6"
	}
	if FLAG ~= "default" then
		sets = {
			setflag .. "xqnetwork_" .. FLAG .. "_black",
			setflag .. "xqnetwork_" .. FLAG .. "_black6"
		}
	end
	tmp_lines = {
		"group proxylist",
		"group-dnl " .. file_proxy_host,
		"group-upstream " .. DNS_TRUST,
		REMOTE_FAKEDNS ~= "1" and "group-ipset " .. table.concat(sets, ",") or ""
	}
	if NO_IPV6_TRUST == "1" then table.insert(tmp_lines, "no-ipv6 tag:proxylist") end
	insert_array_after(config_lines, tmp_lines, "#--3")
	log(string.format("  - 浠ｇ悊鍩熷悕琛?blacklist)锛?s", DNS_TRUST or "榛樿"))
end

--鍐呯疆缁?chn/gfw)浼樺厛绾у湪鑷畾涔夌粍鍚?--GFW鍒楄〃
if GFWLIST == "1" and is_file_nonzero(RULES_PATH .. "/gfwlist") then
	local sets = {
		setflag .. "xqnetwork_gfw",
		setflag .. "xqnetwork_gfw6"
	}
	if FLAG ~= "default" then
		sets = {
			setflag .. "xqnetwork_" .. FLAG .. "_gfw",
			setflag .. "xqnetwork_" .. FLAG .. "_gfw6"
		}
	end
	tmp_lines = {
		"gfwlist-file " .. RULES_PATH .. "/gfwlist",
		REMOTE_FAKEDNS ~= "1" and "add-taggfw-ip " .. table.concat(sets, ",") or ""
	}
	if NO_IPV6_TRUST == "1" then table.insert(tmp_lines, "no-ipv6 tag:gfw") end
	merge_array(config_lines, tmp_lines)
	log(string.format("  - 闃茬伀澧欏煙鍚嶈〃(gfwlist)锛?s", DNS_TRUST or "榛樿"))
end

--涓浗鍒楄〃
if CHNLIST ~= "0" and is_file_nonzero(RULES_PATH .. "/chnlist") then
	if CHNLIST == "direct" then
		tmp_lines = {
			"chnlist-file " .. RULES_PATH .. "/chnlist",
			"ipset-name4 " .. setflag .. "xqnetwork_chn",
			"ipset-name6 " .. setflag .. "xqnetwork_chn6",
			"add-tagchn-ip",
			"chnlist-first"
		}
		merge_array(config_lines, tmp_lines)
		log(string.format("  - 涓浗鍩熷悕琛?chnroute)锛?s", DNS_LOCAL or "榛樿"))
	end

	--鍥炰腑鍥芥ā寮?	if CHNLIST == "proxy" then
		local sets = {
			setflag .. "xqnetwork_chn",
			setflag .. "xqnetwork_chn6"
		}
		tmp_lines = {
			"group chn_proxy",
			"group-dnl " .. RULES_PATH .. "/chnlist",
			"group-upstream " .. DNS_TRUST,
			REMOTE_FAKEDNS ~= "1" and "group-ipset " .. table.concat(sets, ",") or ""
		}
		if NO_IPV6_TRUST == "1" then table.insert(tmp_lines, "no-ipv6 tag:chn_proxy") end
		insert_array_after(config_lines, tmp_lines, "#--1")
		log(string.format("  - 涓浗鍩熷悕琛?chnroute)锛?s", DNS_TRUST or "榛樿"))
	end
end

--鍒嗘祦瑙勫垯
if uci:get(appname, TCP_NODE, "protocol") == "_shunt" then
	local white_domain, lookup_white_domain = {}, {}
	local shunt_domain, lookup_shunt_domain = {}, {}
	local file_white_host = FLAG_PATH .. "/shunt_direct_host"
	local file_shunt_host = FLAG_PATH .. "/shunt_proxy_host"
	local geosite_white_arg, geosite_shunt_arg = "", ""

	local t = uci:get_all(appname, TCP_NODE)
	local default_node_id = t["default_node"] or "_direct"
	uci:foreach(appname, "shunt_rules", function(s)
		local _node_id = t[s[".name"]]
		if _node_id and _node_id ~= "_blackhole" then
			if _node_id == "_default" then
				_node_id = default_node_id
			end

			local domain_list = s.domain_list or ""
			for line in string.gmatch(domain_list, "[^\r\n]+") do
				if line ~= "" and not line:find("#") and not line:find("regexp:") and not line:find("ext:") then
					if line:find("geosite:") then
						line = string.match(line, ":([^:]+)$")
						if _node_id == "_direct" then
							geosite_white_arg = geosite_white_arg .. (geosite_white_arg ~= "" and "," or "") .. line
						else
							geosite_shunt_arg = geosite_shunt_arg .. (geosite_shunt_arg ~= "" and "," or "") .. line
						end
					else
						if line:find("domain:") or line:find("full:") then
							line = string.match(line, ":([^:]+)$")
						end
						line = api.get_std_domain(line)
						if line ~= "" and not line:find("#") then
							if _node_id == "_direct" then
								insert_unique(white_domain, line, lookup_white_domain)
							else
								insert_unique(shunt_domain, line, lookup_shunt_domain)
							end
						end
					end
				end
			end

			if _node_id ~= "_direct" then
				log(string.format("  - Sing-Box/Xray鍒嗘祦瑙勫垯(%s)锛?s", s.remarks, DNS_TRUST or "榛樿"))
			end
		end
	end)

	if is_file_nonzero(file_white_host) == nil then
		if #white_domain > 0 then
			local f_out = io.open(file_white_host, "w")
			for i = 1, #white_domain do
				f_out:write(white_domain[i] .. "\n")
			end
			f_out:close()
		end
	end

	if is_file_nonzero(file_shunt_host) == nil then
		if #shunt_domain > 0 then
			local f_out = io.open(file_shunt_host, "w")
			for i = 1, #shunt_domain do
				f_out:write(shunt_domain[i] .. "\n")
			end
			f_out:close()
		end
	end

	if GFWLIST == "1" and CHNLIST == "0" and USE_GEOVIEW == "1" and api.is_finded("geoview") then  --浠匞FW妯″紡瑙ｆ瀽geosite
		local return_white, return_shunt
		if geosite_white_arg ~= "" then
			return_white = get_geosite(geosite_white_arg, file_white_host)
		end
		if geosite_shunt_arg ~= "" then
			return_shunt = get_geosite(geosite_shunt_arg, file_shunt_host)
		end
		if (return_white == nil or return_white == 0) and (return_shunt == nil or return_shunt == 0) then
			log("  * 瑙ｆ瀽[鍒嗘祦鑺傜偣] Geosite 瀹屾垚")
		else
			log("  * 瑙ｆ瀽[鍒嗘祦鑺傜偣] Geosite 澶辫触锛?)
		end
	end

	local sets = {
		setflag .. "xqnetwork_shunt",
		setflag .. "xqnetwork_shunt6"
	}
	if FLAG ~= "default" then
		sets = {
			setflag .. "xqnetwork_" .. FLAG .. "_shunt",
			setflag .. "xqnetwork_" .. FLAG .. "_shunt6"
		}
	end

	if is_file_nonzero(file_white_host) then
		if USE_DIRECT_LIST == "1" then
			--褰撶櫧鍚嶅崟鍚敤鏃讹紝娣诲姞鍒扮櫧鍚嶅崟缁勪竴鍚屽鐞?			for i, v in ipairs(config_lines) do
				if v == "group-dnl " .. file_direct_host then
					config_lines[i] = "group-dnl " .. file_direct_host .. "," .. file_white_host
					break
				end
			end
		else
			--褰撶櫧鍚嶅崟涓嶅惎鐢ㄦ椂锛屽垱寤烘柊缁勶紝ipset鍒皊huntlist
			tmp_lines = {
				"group whitelist",
				"group-dnl " .. file_white_host,
				"group-upstream " .. DNS_LOCAL,
				"group-ipset " .. table.concat(sets, ",")
			}
			insert_array_after(config_lines, tmp_lines, "#--4")
		end
		
	end

	if is_file_nonzero(file_shunt_host) then
		tmp_lines = {
			"group shuntlist",
			"group-dnl " .. file_shunt_host,
			"group-upstream " .. DNS_TRUST,
			(not only_global and REMOTE_FAKEDNS == "1") and "" or ("group-ipset " .. table.concat(sets, ","))
		}
		if NO_IPV6_TRUST == "1" then table.insert(tmp_lines, "no-ipv6 tag:shuntlist") end
		insert_array_after(config_lines, tmp_lines, "#--2")
	end

end

--鍙娇鐢╣fwlist妯″紡锛孏FW鍒楄〃浠ュ鐨勫煙鍚嶅強榛樿浣跨敤鏈湴DNS
if GFWLIST == "1" and CHNLIST == "0" then DEFAULT_TAG = "chn" end

--鍥炰腑鍥芥ā寮忥紝涓浗鍒楄〃浠ュ鐨勫煙鍚嶅強榛樿浣跨敤鏈湴DNS
if CHNLIST == "proxy" then DEFAULT_TAG = "chn" end

--鍏ㄥ眬妯″紡锛岄粯璁や娇鐢ㄨ繙绋婦NS
if only_global then
	DEFAULT_TAG = "gfw"
	if NO_IPV6_TRUST == "1" and uci:get(appname, TCP_NODE, "protocol") ~= "_shunt" then 
		table.insert(config_lines, "no-ipv6")
	end
end

--鏄惁鎺ュ彈鐩磋繛 DNS 绌哄搷搴?if DEFAULT_TAG == "none_noip" then table.insert(config_lines, "noip-as-chnip") end

if DEFAULT_TAG == nil or DEFAULT_TAG == "smart" or DEFAULT_TAG == "none_noip" then DEFAULT_TAG = "none" end

table.insert(config_lines, "default-tag " .. DEFAULT_TAG)

if DEFAULT_TAG == "none" then
	table.insert(config_lines, "verdict-cache 5000")
end

table.insert(config_lines, "hosts")

local cert_verify = uci:get(appname, "@global[0]", "chinadns_ng_cert_verify") or 0
if tonumber(cert_verify) == 1 then
	table.insert(config_lines, "cert-verify")
end

if DEFAULT_TAG == "chn" then
	log(string.format("  - 榛樿 DNS 锛?s", DNS_LOCAL))
elseif  DEFAULT_TAG == "gfw" then
	log(string.format("  - 榛樿 DNS 锛?s", DNS_TRUST))
else
	log(string.format("  - 榛樿 DNS 锛?s", "鏅鸿兘鍖归厤"))
end

--杈撳嚭閰嶇疆鏂囦欢
if #config_lines > 0 then
	for i = 1, #config_lines do
		line = config_lines[i]
		if line ~= "" and not line:find("^#--") then
			print(line)
		end
	end
end

log("  - ChinaDNS-NG宸蹭綔涓篋nsmasq涓婃父锛屽鏋滀綘鑷閰嶇疆浜嗛敊璇殑DNS娴佺▼锛屽皢浼氬鑷村煙鍚?鐩磋繛/浠ｇ悊鍩熷悕)鍒嗘祦澶辨晥锛侊紒锛?)

