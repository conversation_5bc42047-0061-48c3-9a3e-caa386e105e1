﻿#!/bin/bash

# 鎵归噺鏇挎崲 xqnetwork 涓?xqnetwork 鐨勮剼鏈?
set -e

# 棰滆壊瀹氫箟
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 宸ヤ綔鐩綍
WORK_DIR="luci-app-xqnetwork"

if [ ! -d "$WORK_DIR" ]; then
    print_error "鐩綍 $WORK_DIR 涓嶅瓨鍦?
    exit 1
fi

print_info "寮€濮嬫壒閲忔浛鎹?xqnetwork 涓?xqnetwork..."

# 1. 鏇挎崲鏂囦欢鍐呭涓殑 xqnetwork
print_info "鏇挎崲鏂囦欢鍐呭..."

# 鏌ユ壘鎵€鏈夐渶瑕佹浛鎹㈢殑鏂囦欢绫诲瀷
find "$WORK_DIR" -type f \( -name "*.lua" -o -name "*.sh" -o -name "*.htm" -o -name "*.js" -o -name "*.json" -o -name "*.conf" -o -name "*.config" -o -name "*default_config*" \) | while read file; do
    if grep -q "xqnetwork" "$file" 2>/dev/null; then
        print_info "澶勭悊鏂囦欢: $file"
        # 浣跨敤 sed 鏇挎崲锛屼絾淇濈暀涓€浜涚壒娈婃儏鍐?        sed -i 's/xqnetwork/xqnetwork/g' "$file"
        # 淇涓€浜涘彲鑳界殑閿欒鏇挎崲
        sed -i 's/xqnetworkd/xqnetworkd/g' "$file"
        sed -i 's/xqnetwork_server/xqnetwork_server/g' "$file"
    fi
done

# 2. 閲嶅懡鍚嶅寘鍚?xqnetwork 鐨勭洰褰?print_info "閲嶅懡鍚嶇洰褰?.."

# 閲嶅懡鍚?xqnetwork 鐩綍涓?xqnetwork
if [ -d "$WORK_DIR/luasrc/xqnetwork" ]; then
    mv "$WORK_DIR/luasrc/xqnetwork" "$WORK_DIR/luasrc/xqnetwork"
    print_success "閲嶅懡鍚嶇洰褰? luasrc/xqnetwork -> luasrc/xqnetwork"
fi

if [ -d "$WORK_DIR/luasrc/model/cbi/xqnetwork" ]; then
    mv "$WORK_DIR/luasrc/model/cbi/xqnetwork" "$WORK_DIR/luasrc/model/cbi/xqnetwork"
    print_success "閲嶅懡鍚嶇洰褰? luasrc/model/cbi/xqnetwork -> luasrc/model/cbi/xqnetwork"
fi

if [ -d "$WORK_DIR/luasrc/view/xqnetwork" ]; then
    mv "$WORK_DIR/luasrc/view/xqnetwork" "$WORK_DIR/luasrc/view/xqnetwork"
    print_success "閲嶅懡鍚嶇洰褰? luasrc/view/xqnetwork -> luasrc/view/xqnetwork"
fi

if [ -d "$WORK_DIR/root/usr/share/xqnetwork" ]; then
    mv "$WORK_DIR/root/usr/share/xqnetwork" "$WORK_DIR/root/usr/share/xqnetwork"
    print_success "閲嶅懡鍚嶇洰褰? root/usr/share/xqnetwork -> root/usr/share/xqnetwork"
fi

# 3. 閲嶅懡鍚嶅寘鍚?xqnetwork 鐨勬枃浠?print_info "閲嶅懡鍚嶆枃浠?.."

find "$WORK_DIR" -type f -name "*xqnetwork*" | while read file; do
    newfile=$(echo "$file" | sed 's/xqnetwork/xqnetwork/g')
    if [ "$file" != "$newfile" ]; then
        mv "$file" "$newfile"
        print_success "閲嶅懡鍚嶆枃浠? $(basename "$file") -> $(basename "$newfile")"
    fi
done

# 4. 鏇存柊璺緞寮曠敤
print_info "鏇存柊璺緞寮曠敤..."

find "$WORK_DIR" -type f \( -name "*.lua" -o -name "*.sh" -o -name "*.htm" \) | while read file; do
    if grep -q "xqnetwork" "$file" 2>/dev/null; then
        # 鏇存柊璺緞寮曠敤
        sed -i 's|luci\.xqnetwork|luci.xqnetwork|g' "$file"
        sed -i 's|/xqnetwork/|/xqnetwork/|g' "$file"
        sed -i 's|xqnetwork/|xqnetwork/|g' "$file"
        sed -i 's|"xqnetwork"|"xqnetwork"|g' "$file"
        sed -i "s|'xqnetwork'|'xqnetwork'|g" "$file"
    fi
done

# 5. 鏇存柊鎺у埗鍣ㄦ枃浠跺悕
if [ -f "$WORK_DIR/luasrc/controller/xqnetwork.lua" ]; then
    mv "$WORK_DIR/luasrc/controller/xqnetwork.lua" "$WORK_DIR/luasrc/controller/xqnetwork.lua"
    print_success "閲嶅懡鍚嶆帶鍒跺櫒: xqnetwork.lua -> xqnetwork.lua"
fi

# 6. 鏇存柊 init 鑴氭湰
if [ -f "$WORK_DIR/root/etc/init.d/xqnetwork" ]; then
    mv "$WORK_DIR/root/etc/init.d/xqnetwork" "$WORK_DIR/root/etc/init.d/xqnetwork"
    print_success "閲嶅懡鍚?init 鑴氭湰: xqnetwork -> xqnetwork"
fi

if [ -f "$WORK_DIR/root/etc/init.d/xqnetwork_server" ]; then
    mv "$WORK_DIR/root/etc/init.d/xqnetwork_server" "$WORK_DIR/root/etc/init.d/xqnetwork_server"
    print_success "閲嶅懡鍚?init 鑴氭湰: xqnetwork_server -> xqnetwork_server"
fi

# 7. 鏇存柊閰嶇疆鏂囦欢璺緞
find "$WORK_DIR" -type f \( -name "*.lua" -o -name "*.sh" \) | while read file; do
    if grep -q "/etc/config/xqnetwork" "$file" 2>/dev/null; then
        sed -i 's|/etc/config/xqnetwork|/etc/config/xqnetwork|g' "$file"
    fi
done

# 8. 鐗规畩澶勭悊锛氭洿鏂版ā鏉胯矾寰?find "$WORK_DIR" -type f -name "*.lua" | while read file; do
    if grep -q 'template.*xqnetwork' "$file" 2>/dev/null; then
        sed -i 's|template.*=.*"xqnetwork/|template = "xqnetwork/|g' "$file"
    fi
done

print_success "鎵归噺鏇挎崲瀹屾垚锛?
print_info "璇锋鏌ヤ互涓嬪唴瀹癸細"
echo "1. 鎵€鏈夋枃浠跺唴瀹逛腑鐨?xqnetwork 宸叉浛鎹负 xqnetwork"
echo "2. 鎵€鏈夌洰褰曞悕涓殑 xqnetwork 宸叉浛鎹负 xqnetwork"
echo "3. 鎵€鏈夋枃浠跺悕涓殑 xqnetwork 宸叉浛鎹负 xqnetwork"
echo "4. 鎵€鏈夎矾寰勫紩鐢ㄥ凡鏇存柊"

print_warning "璇锋墜鍔ㄦ鏌ヤ互涓嬫枃浠舵槸鍚﹂渶瑕侀澶栦慨鏀癸細"
echo "- Makefile"
echo "- README.md"
echo "- 閰嶇疆鏂囦欢"

